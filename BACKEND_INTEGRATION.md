# 前端与Flask后端集成指南

## 🔗 连接配置

### 1. 环境变量配置
已在 `.env` 文件中配置：
```
VITE_API_BASE_URL=http://127.0.0.1:5000/api
```

### 2. API基础信息
- **后端地址**: `http://127.0.0.1:5000`
- **API前缀**: `/api`
- **完整API地址**: `http://127.0.0.1:5000/api`

## 🚀 启动步骤

### 启动后端服务
1. 确保Flask后端服务运行在 `http://127.0.0.1:5000`
2. 确保后端已配置CORS支持跨域请求

### 启动前端服务
```bash
pnpm dev
```
前端将运行在 `http://localhost:5173`

## 📡 API测试

### 自动测试
- 访问首页时会自动测试API连接
- 页面会显示"后端API状态"指示器
- 绿色表示连接成功，红色表示连接失败

### 手动测试
点击首页的"重新测试"按钮可手动测试API连接

## 🔧 API接口映射

### 认证相关
- 登录: `POST /api/auth/login`
- 注册: `POST /api/auth/register`
- 登出: `POST /api/auth/logout`
- 刷新令牌: `POST /api/auth/refresh`

### 房源相关
- 获取房源列表: `GET /api/houses`
- 获取房源详情: `GET /api/houses/{id}`
- 创建房源: `POST /api/houses`
- 更新房源: `PUT /api/houses/{id}`

## 🛠️ 故障排除

### 如果API连接失败
1. 检查Flask后端是否正在运行
2. 检查后端地址是否为 `http://127.0.0.1:5000`
3. 检查后端是否配置了CORS
4. 查看浏览器控制台的错误信息

### 常见问题
- **CORS错误**: 确保Flask后端配置了Flask-CORS
- **网络错误**: 检查后端服务是否启动
- **404错误**: 检查API路径是否正确

## 📝 下一步

1. 启动您的Flask后端服务
2. 访问 `http://localhost:5173` 查看前端
3. 检查首页的API状态指示器
4. 如果连接成功，可以开始测试登录、注册等功能
