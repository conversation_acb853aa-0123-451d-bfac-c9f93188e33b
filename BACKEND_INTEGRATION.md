# 🚀 前端与Flask后端完整集成指南

## 🎉 已恢复的完整功能

### ✅ 核心功能
1. **完整的首页** - 包含横幅、API状态检测、房源展示、快速导航
2. **用户认证系统** - 登录、注册、登出功能
3. **房源管理** - 房源列表、详情、创建、更新
4. **响应式布局** - 支持桌面和移动设备
5. **Element Plus UI** - 完整的组件库和图标支持
6. **API拦截器** - 自动处理认证和错误

### ✅ 页面功能
- **首页** (`/`) - 展示系统概览和API连接状态
- **登录页** (`/login`) - 用户登录
- **注册页** (`/register`) - 用户注册
- **房源列表** (`/houses`) - 浏览所有房源
- **房源详情** (`/houses/:id`) - 查看房源详细信息
- **用户仪表板** - 根据角色显示不同功能

## 🔗 API连接配置

### 环境变量 (.env)
```env
VITE_API_BASE_URL=http://127.0.0.1:5000/api
VITE_APP_TITLE=智能房屋租赁系统
VITE_APP_ENV=development
```

### 后端要求
- **Flask服务地址**: `http://127.0.0.1:5000`
- **API路径前缀**: `/api`
- **CORS支持**: 必须配置跨域请求
- **认证方式**: JWT Token (Bearer)

## 📡 API接口详细映射

### 🔐 认证接口
```typescript
// 登录
POST /api/auth/login
Body: { username: string, password: string }
Response: { id: number, username: string, role: string, token: string }

// 注册
POST /api/auth/register
Body: { username: string, email: string, password: string, role: string, full_name: string, phone_number: string }
Response: { id: number, username: string, role: string, token: string }

// 登出
POST /api/auth/logout
Headers: { Authorization: "Bearer <token>" }
Response: { message: string }

// 刷新令牌
POST /api/auth/refresh
Headers: { Authorization: "Bearer <token>" }
Response: { token: string }
```

### 🏠 房源接口
```typescript
// 获取房源列表
GET /api/houses?page=1&per_page=10&city=北京&min_price=1000&max_price=5000
Response: { items: HouseListItem[], total: number, page: number, per_page: number }

// 获取房源详情
GET /api/houses/{id}
Response: HouseDetail

// 创建房源 (房东)
POST /api/houses
Headers: { Authorization: "Bearer <token>" }
Body: CreateHouseRequest
Response: { id: number, title: string, status: string, message: string }

// 更新房源 (房东)
PUT /api/houses/{id}
Headers: { Authorization: "Bearer <token>" }
Body: CreateHouseRequest
Response: { id: number, title: string, message: string }

// 上传房源图片 (房东)
POST /api/houses/{id}/images
Headers: { Authorization: "Bearer <token>", Content-Type: "multipart/form-data" }
Body: FormData with image files
Response: { images: HouseImage[] }
```

### 📊 其他功能模块
```typescript
// 用户管理
GET /api/users
Response: UserListItem[]

// 消息管理
GET /api/messages
Response: Message[]

// 租赁管理
GET /api/leases
Response: Lease[]

// 维修管理
GET /api/repairs
Response: Repair[]

// 报表统计
GET /api/reports
Response: DashboardStats | RevenueReport[] | HouseReport[] | UserReport[]

// 管理员功能
GET /api/admin
Response: SystemSettings | AuditLog[] | SystemInfo
```

## 🚀 启动指南

### 1. 启动前端 (已完成)
```bash
pnpm dev
# 前端运行在: http://localhost:5173
```

### 2. 启动Flask后端
```bash
# 在您的Flask项目目录中
python app.py
# 后端应运行在: http://127.0.0.1:5000
```

### 3. 验证连接
1. 访问 `http://localhost:5173`
2. 查看首页的"🔗 后端API连接状态"
3. 如果显示绿色"连接成功"，说明前后端已连通

## 🎯 测试功能

### API连接测试
- 首页会自动测试 `/api/health` 端点
- 显示连接状态和后端地址
- 可手动重新测试连接

### 用户功能测试
1. **注册新用户** - 点击"用户注册"
2. **用户登录** - 点击"用户登录"
3. **浏览房源** - 点击"浏览房源"

### 房源功能测试
- 如果后端有房源数据，首页会显示"🏠 特色房源"
- 点击房源卡片可查看详情
- 登录后可根据角色进行相应操作

## 🛠️ 故障排除

### 常见问题
1. **空白页面** ✅ 已解决 - Vue运行时编译配置
2. **Axios导入错误** ✅ 已解决 - 类型导入方式
3. **API连接失败** - 检查Flask服务是否启动
4. **CORS错误** - 确保Flask配置了Flask-CORS

### 调试步骤
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签的错误信息
3. 查看Network标签的请求状态
4. 检查API响应数据格式

## 📋 下一步开发

现在前端已完全恢复，您可以：

1. **完善后端API** - 实现对应的Flask路由
2. **测试用户流程** - 注册、登录、浏览房源
3. **添加业务逻辑** - 房源管理、租赁流程
4. **优化用户体验** - 根据实际使用调整界面

前端已准备就绪，等待与您的Flask后端进行完整交互！🎉
