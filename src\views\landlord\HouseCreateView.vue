<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import houseService from '@/services/house.service'
import type { CreateHouseRequest } from '@/services/house.service'

const router = useRouter()

// 表单引用
const houseFormRef = ref<FormInstance>()

// 表单数据
const houseForm = reactive<CreateHouseRequest>({
  title: '',
  description: '',
  address: '',
  city: '',
  district: '',
  price_monthly: 0,
  deposit_amount: 0,
  bedrooms: 1,
  bathrooms: 1,
  area_sqm: 0,
  floor: 1,
  total_floors: 1,
  has_elevator: false,
  amenities: [],
  available_from: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入房源标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应为5-100个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入房源描述', trigger: 'blur' },
    { min: 20, max: 1000, message: '描述长度应为20-1000个字符', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  city: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ],
  district: [
    { required: true, message: '请选择区域', trigger: 'change' }
  ],
  price_monthly: [
    { required: true, message: '请输入月租金', trigger: 'blur' },
    { type: 'number', min: 1, message: '月租金必须大于0', trigger: 'blur' }
  ],
  deposit_amount: [
    { required: true, message: '请输入押金', trigger: 'blur' },
    { type: 'number', min: 0, message: '押金不能小于0', trigger: 'blur' }
  ],
  bedrooms: [
    { required: true, message: '请选择卧室数量', trigger: 'change' }
  ],
  bathrooms: [
    { required: true, message: '请选择卫生间数量', trigger: 'change' }
  ],
  area_sqm: [
    { required: true, message: '请输入建筑面积', trigger: 'blur' },
    { type: 'number', min: 1, message: '建筑面积必须大于0', trigger: 'blur' }
  ],
  floor: [
    { required: true, message: '请输入楼层', trigger: 'blur' },
    { type: 'number', min: 1, message: '楼层必须大于0', trigger: 'blur' }
  ],
  total_floors: [
    { required: true, message: '请输入总楼层', trigger: 'blur' },
    { type: 'number', min: 1, message: '总楼层必须大于0', trigger: 'blur' }
  ],
  available_from: [
    { required: true, message: '请选择可入住日期', trigger: 'change' }
  ]
})

// 城市选项
const cityOptions = [
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '杭州', label: '杭州' },
  { value: '南京', label: '南京' }
]

// 区域选项（根据城市动态变化）
const districtOptions = ref<{ value: string; label: string }[]>([])

// 配套设施选项
const amenityOptions = [
  '空调', '洗衣机', '冰箱', '热水器', '电视', 'WiFi',
  '床', '衣柜', '书桌', '沙发', '餐桌', '微波炉',
  '停车位', '阳台', '独立卫生间', '24小时热水'
]

// 上传的图片列表
const imageList = ref<File[]>([])
const imageUrls = ref<string[]>([])
const loading = ref(false)

// 监听城市变化
const handleCityChange = (city: string) => {
  houseForm.district = ''
  
  // 根据城市设置区域选项
  const districtMap: Record<string, string[]> = {
    '北京': ['朝阳区', '海淀区', '东城区', '西城区', '丰台区', '石景山区'],
    '上海': ['浦东新区', '徐汇区', '静安区', '黄浦区', '长宁区', '普陀区'],
    '广州': ['天河区', '越秀区', '荔湾区', '海珠区', '白云区', '番禺区'],
    '深圳': ['南山区', '福田区', '罗湖区', '宝安区', '龙岗区', '盐田区']
  }
  
  districtOptions.value = (districtMap[city] || []).map(district => ({
    value: district,
    label: district
  }))
}

// 处理图片上传
const handleImageUpload = (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  
  // 检查文件大小
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB')
    return false
  }
  
  // 检查图片数量
  if (imageList.value.length >= 10) {
    ElMessage.error('最多只能上传10张图片')
    return false
  }
  
  // 添加到图片列表
  imageList.value.push(file)
  imageUrls.value.push(URL.createObjectURL(file))
  
  return false
}

// 移除图片
const removeImage = (index: number) => {
  imageList.value.splice(index, 1)
  imageUrls.value.splice(index, 1)
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      if (imageList.value.length === 0) {
        ElMessage.error('请至少上传一张房源图片')
        return
      }
      
      try {
        loading.value = true
        
        // 1. 创建房源
        const response = await houseService.createHouse(houseForm)
        
        // 2. 上传图片
        if (imageList.value.length > 0) {
          await houseService.uploadHouseImages(response.id, imageList.value)
        }
        
        ElMessage.success('房源发布成功')
        
        // 跳转到房源列表页
        router.push('/landlord/houses')
      } catch (error) {
        console.error('Failed to create house:', error)
        ElMessage.error('发布房源失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 取消
const cancel = () => {
  router.push('/landlord/houses')
}
</script>

<template>
  <div class="house-create-container">
    <div class="page-header">
      <h1>发布房源</h1>
    </div>
    
    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="houseFormRef"
        :model="houseForm"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="房源标题" prop="title">
              <el-input v-model="houseForm.title" placeholder="请输入房源标题，如：阳光花园精装两居室" />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="房源描述" prop="description">
              <el-input
                v-model="houseForm.description"
                type="textarea"
                :rows="4"
                placeholder="请详细描述房源的特点、周边环境、交通情况等"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-select v-model="houseForm.city" placeholder="选择城市" @change="handleCityChange">
                <el-option
                  v-for="item in cityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="区域" prop="district">
              <el-select v-model="houseForm.district" placeholder="选择区域" :disabled="!houseForm.city">
                <el-option
                  v-for="item in districtOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="houseForm.address" placeholder="请输入详细地址，如：朝阳区阳光花园小区3号楼2单元101室" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="月租金(元)" prop="price_monthly">
              <el-input-number v-model="houseForm.price_monthly" :min="1" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="押金(元)" prop="deposit_amount">
              <el-input-number v-model="houseForm.deposit_amount" :min="0" :step="100" style="width: 100%" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="卧室数量" prop="bedrooms">
              <el-select v-model="houseForm.bedrooms" placeholder="选择卧室数量">
                <el-option :value="1" label="1室" />
                <el-option :value="2" label="2室" />
                <el-option :value="3" label="3室" />
                <el-option :value="4" label="4室" />
                <el-option :value="5" label="5室及以上" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="卫生间数量" prop="bathrooms">
              <el-select v-model="houseForm.bathrooms" placeholder="选择卫生间数量">
                <el-option :value="1" label="1卫" />
                <el-option :value="2" label="2卫" />
                <el-option :value="3" label="3卫" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="建筑面积(㎡)" prop="area_sqm">
              <el-input-number v-model="houseForm.area_sqm" :min="1" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="楼层" prop="floor">
              <el-input-number v-model="houseForm.floor" :min="1" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="总楼层" prop="total_floors">
              <el-input-number v-model="houseForm.total_floors" :min="1" :step="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="电梯">
              <el-switch v-model="houseForm.has_elevator" active-text="有" inactive-text="无" />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="可入住日期" prop="available_from">
              <el-date-picker
                v-model="houseForm.available_from"
                type="date"
                placeholder="选择可入住日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="date => date.getTime() < Date.now() - 8.64e7"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="配套设施">
              <el-checkbox-group v-model="houseForm.amenities">
                <el-checkbox
                  v-for="amenity in amenityOptions"
                  :key="amenity"
                  :label="amenity"
                >
                  {{ amenity }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="房源图片" required>
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :show-file-list="false"
                :before-upload="handleImageUpload"
                :disabled="loading"
              >
                <el-icon><plus /></el-icon>
              </el-upload>
              
              <div class="image-preview" v-if="imageUrls.length > 0">
                <div v-for="(url, index) in imageUrls" :key="index" class="image-item">
                  <img :src="url" :alt="`图片 ${index + 1}`">
                  <div class="image-actions">
                    <el-button type="danger" circle @click="removeImage(index)">
                      <el-icon><delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </div>
              
              <div class="upload-tip">
                <p>提示: 请上传1-10张房源图片，每张图片不超过5MB，支持jpg、png、jpeg格式</p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm(houseFormRef)" :loading="loading">
            发布房源
          </el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.house-create-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-card {
  margin-bottom: 20px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.image-item {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  overflow: hidden;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
}

.upload-tip {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
