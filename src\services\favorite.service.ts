import api from './api'
import type { PaginatedResponse } from './user.service'

export interface FavoriteHouse {
  id: number
  house: {
    id: number
    title: string
    address: string
    city: string
    district: string
    price_monthly: number
    bedrooms: number
    main_image_url: string
  }
  created_at: string
}

export interface AddFavoriteRequest {
  house_id: number
}

export interface MessageResponse {
  message: string
}

export interface CheckFavoriteResponse {
  is_favorite: boolean
  favorite_id: number | null
}

class FavoriteService {
  /**
   * 获取收藏房源列表
   * @param params 查询参数
   */
  async getFavoriteList(params?: {
    page?: number
    per_page?: number
  }): Promise<PaginatedResponse<FavoriteHouse>> {
    const response = await api.get<PaginatedResponse<FavoriteHouse>>('/favorites', { params })
    return response.data
  }

  /**
   * 添加收藏
   * @param data 收藏信息
   */
  async addFavorite(data: AddFavoriteRequest): Promise<{ id: number; message: string }> {
    const response = await api.post<{ id: number; message: string }>('/favorites', data)
    return response.data
  }

  /**
   * 取消收藏
   * @param favoriteId 收藏ID
   */
  async removeFavorite(favoriteId: number): Promise<MessageResponse> {
    const response = await api.delete<MessageResponse>(`/favorites/${favoriteId}`)
    return response.data
  }

  /**
   * 检查房源是否已收藏
   * @param houseId 房源ID
   */
  async checkFavorite(houseId: number): Promise<CheckFavoriteResponse> {
    const response = await api.get<CheckFavoriteResponse>(`/favorites/check/${houseId}`)
    return response.data
  }
}

export default new FavoriteService()
