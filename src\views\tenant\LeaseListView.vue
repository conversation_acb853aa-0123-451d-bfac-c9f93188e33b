<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import leaseService from '@/services/lease.service'
import type { Lease } from '@/services/lease.service'

const router = useRouter()
const leases = ref<Lease[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待签署', label: '待签署' },
  { value: '生效中', label: '生效中' },
  { value: '已到期', label: '已到期' },
  { value: '已解除', label: '已解除' }
]

// 获取租约列表
const fetchLeases = async () => {
  try {
    loading.value = true

    const response = await leaseService.getLeaseList({
      status: filters.status,
      page: filters.page,
      per_page: filters.per_page
    })

    leases.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch leases:', error)
    ElMessage.error('获取租约列表失败')
  } finally {
    loading.value = false
  }
}

// 查看租约详情
const viewLeaseDetail = (leaseId: number) => {
  router.push(`/tenant/leases/${leaseId}`)
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 签署租约
const signLease = async (leaseId: number) => {
  try {
    await leaseService.signLease(leaseId)

    // 更新状态
    const index = leases.value.findIndex(item => item.id === leaseId)
    if (index !== -1) {
      leases.value[index].status = '生效中'
    }

    ElMessage.success('租约签署成功')
  } catch (error) {
    console.error('Failed to sign lease:', error)
    ElMessage.error('签署租约失败')
  }
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchLeases()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchLeases()
}

// 计算租约剩余天数
const calculateRemainingDays = (endDate: string) => {
  const end = new Date(endDate)
  const today = new Date()
  const diffTime = end.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

onMounted(() => {
  fetchLeases()
})
</script>

<template>
  <div class="lease-list-container">
    <div class="page-header">
      <h1>我的租约</h1>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <div class="lease-list" v-loading="loading">
      <el-empty v-if="leases.length === 0 && !loading" description="暂无租约记录" />

      <el-card v-for="lease in leases" :key="lease.id" class="lease-item">
        <div class="lease-content">
          <div class="house-image" @click="viewHouseDetail(lease.house.id)">
            <img :src="lease.house.main_image_url" :alt="lease.house.title">
          </div>

          <div class="lease-info">
            <h3 class="house-title" @click="viewHouseDetail(lease.house.id)">
              {{ lease.house.title }}
            </h3>

            <p class="house-address">
              {{ lease.house.address }}
            </p>

            <div class="lease-details">
              <div class="detail-item">
                <span class="label">租期:</span>
                <span class="value">{{ lease.start_date }} 至 {{ lease.end_date }}</span>
              </div>

              <div class="detail-item">
                <span class="label">月租金:</span>
                <span class="value price">¥{{ lease.rent_amount_monthly }}</span>
              </div>

              <div class="detail-item">
                <span class="label">房东:</span>
                <span class="value">{{ lease.landlord.full_name }}</span>
              </div>

              <div class="detail-item">
                <span class="label">状态:</span>
                <el-tag
                  :type="
                    lease.status === '生效中' ? 'success' :
                    lease.status === '待签署' ? 'warning' :
                    lease.status === '已到期' ? 'info' : 'danger'
                  "
                >
                  {{ lease.status }}
                </el-tag>
              </div>

              <div class="detail-item" v-if="lease.status === '生效中'">
                <span class="label">剩余天数:</span>
                <span class="value">{{ calculateRemainingDays(lease.end_date) }}天</span>
              </div>
            </div>
          </div>

          <div class="lease-actions">
            <el-button type="primary" @click="viewLeaseDetail(lease.id)">查看详情</el-button>
            <el-button
              type="success"
              v-if="lease.status === '待签署'"
              @click="signLease(lease.id)"
            >
              签署租约
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.lease-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.lease-list {
  min-height: 400px;
}

.lease-item {
  margin-bottom: 20px;
}

.lease-content {
  display: flex;
  align-items: flex-start;
}

.house-image {
  width: 150px;
  height: 100px;
  overflow: hidden;
  border-radius: 4px;
  margin-right: 20px;
  cursor: pointer;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.house-image:hover img {
  transform: scale(1.05);
}

.lease-info {
  flex: 1;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
  cursor: pointer;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  margin: 0 0 10px 0;
  color: #909399;
}

.lease-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #303133;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.lease-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
