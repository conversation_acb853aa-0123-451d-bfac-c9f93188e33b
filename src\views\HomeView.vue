<script setup lang="ts">
// 最简单的测试版本
</script>

<template>
  <div class="home-container">
    <h1>智能房屋租赁系统</h1>
    <p>页面正常显示！</p>
    <button>测试按钮</button>
  </div>
</template>

<style scoped>
.home-container {
  padding: 50px;
  text-align: center;
  background: #f0f0f0;
  min-height: 100vh;
}

h1 {
  color: #333;
  font-size: 36px;
}

p {
  color: #666;
  font-size: 18px;
}

button {
  padding: 10px 20px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>
