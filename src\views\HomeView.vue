<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/layout/MainLayout.vue'
import api from '@/services/api'

const router = useRouter()
const loading = ref(false)
const apiStatus = ref('未测试')

// 测试API连接
const testApiConnection = async () => {
  try {
    loading.value = true
    apiStatus.value = '测试中...'

    // 尝试访问一个简单的API端点
    const response = await api.get('/health')

    apiStatus.value = '连接成功'
    ElMessage.success('API连接成功！')
    console.log('API响应:', response.data)
  } catch (error) {
    apiStatus.value = '连接失败'
    ElMessage.error('API连接失败，请检查后端服务是否启动')
    console.error('API连接错误:', error)
  } finally {
    loading.value = false
  }
}

// 跳转到房源列表页
const viewAllHouses = () => {
  router.push('/houses')
}

// 跳转到登录页
const goToLogin = () => {
  router.push('/login')
}

// 跳转到注册页
const goToRegister = () => {
  router.push('/register')
}

// 页面加载时自动测试API连接
onMounted(() => {
  testApiConnection()
})
</script>

<template>
  <main-layout>
    <!-- 首页横幅 -->
    <div class="banner">
      <div class="banner-content">
        <h1>找到您理想的住所</h1>
        <p>智能房屋租赁系统，让租房变得简单、高效、安心</p>
        <div class="search-box">
          <el-input
            placeholder="输入城市、区域或小区名称"
            class="search-input"
            clearable
          >
            <template #append>
              <el-button type="primary" @click="viewAllHouses">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- API状态 -->
    <div class="section">
      <div class="api-status">
        <h3>后端API状态</h3>
        <el-tag
          :type="apiStatus === '连接成功' ? 'success' : apiStatus === '连接失败' ? 'danger' : 'info'"
          size="large"
        >
          {{ apiStatus }}
        </el-tag>
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="testApiConnection"
          style="margin-left: 10px;"
        >
          重新测试
        </el-button>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="section">
      <div class="section-header">
        <h2>快速开始</h2>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8">
          <el-card class="nav-card" @click="viewAllHouses">
            <div class="nav-content">
              <el-icon class="nav-icon"><House /></el-icon>
              <h3>浏览房源</h3>
              <p>查看所有可租房源</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="nav-card" @click="goToLogin">
            <div class="nav-content">
              <el-icon class="nav-icon"><User /></el-icon>
              <h3>用户登录</h3>
              <p>登录您的账户</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="nav-card" @click="goToRegister">
            <div class="nav-content">
              <el-icon class="nav-icon"><UserFilled /></el-icon>
              <h3>用户注册</h3>
              <p>创建新账户</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务特点 -->
    <div class="section bg-light">
      <h2 class="section-title text-center">我们的服务</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><House /></el-icon>
            <h3>丰富的房源</h3>
            <p>提供各类型、各价位的优质房源，满足不同需求</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><DocumentChecked /></el-icon>
            <h3>安全保障</h3>
            <p>所有房源经过严格审核，确保真实可靠</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><Service /></el-icon>
            <h3>贴心服务</h3>
            <p>提供从看房到签约的全流程服务支持</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </main-layout>
</template>

<style scoped>
.banner {
  height: 500px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 40px;
  border-radius: 8px;
}

.banner-content {
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.banner h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.banner p {
  font-size: 20px;
  margin-bottom: 30px;
}

.search-box {
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.section {
  margin-bottom: 40px;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 30px;
}

.api-status {
  text-align: center;
  padding: 20px;
}

.api-status h3 {
  margin-bottom: 15px;
  color: #303133;
}

.nav-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.nav-card:hover {
  transform: translateY(-5px);
}

.nav-content {
  text-align: center;
  padding: 20px;
}

.nav-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 15px;
}

.nav-content h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.nav-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.bg-light {
  background-color: #f5f7fa;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}
</style>
