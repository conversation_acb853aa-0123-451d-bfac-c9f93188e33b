<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/layout/MainLayout.vue'
import api from '@/services/api'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()
const loading = ref(false)
const apiStatus = ref('未测试')
const featuredHouses = ref<HouseListItem[]>([])

// 测试API连接
const testApiConnection = async () => {
  try {
    loading.value = true
    apiStatus.value = '测试中...'

    // 使用实际存在的房源列表接口来测试连接
    const response = await api.get('/houses')

    apiStatus.value = '连接成功'
    ElMessage.success('后端API连接成功！')
    console.log('API响应:', response.data)

    // 连接成功后，直接使用获取到的房源数据
    if (response.data && Array.isArray(response.data)) {
      featuredHouses.value = response.data.slice(0, 6) // 取前6个作为特色房源
      ElMessage.success(`成功获取 ${response.data.length} 个房源`)
    }
  } catch (error) {
    apiStatus.value = '连接失败'
    ElMessage.error('后端API连接失败，请检查Flask服务是否启动在 http://127.0.0.1:5000')
    console.error('API连接错误:', error)
  } finally {
    loading.value = false
  }
}

// 获取推荐房源
const fetchFeaturedHouses = async () => {
  try {
    const response = await houseService.getHouseList()
    featuredHouses.value = response.slice(0, 6) // 取前6个作为特色房源
    ElMessage.success(`成功获取 ${response.length} 个房源`)
  } catch (error) {
    console.error('获取房源失败:', error)
    ElMessage.warning('获取房源数据失败，可能是后端API尚未实现')
  }
}

// 跳转到房源详情页
const viewHouseDetail = (id: number) => {
  router.push(`/houses/${id}`)
}

// 跳转到房源列表页
const viewAllHouses = () => {
  router.push('/houses')
}

// 跳转到登录页
const goToLogin = () => {
  router.push('/login')
}

// 跳转到注册页
const goToRegister = () => {
  router.push('/register')
}

// 系统功能流程数据
const systemFeatures = [
  {
    category: '🔐 认证系统',
    color: '#409EFF',
    features: [
      {
        title: '用户注册',
        description: '角色选择 → 自动跳转专属仪表板',
        icon: 'UserFilled',
        route: '/register'
      },
      {
        title: '用户登录',
        description: '身份验证 → 根据角色显示对应功能',
        icon: 'User',
        route: '/login'
      },
      {
        title: '安全设置',
        description: '密码修改 → 安全验证 → MFA设置',
        icon: 'Lock',
        route: '/profile/security'
      }
    ]
  },
  {
    category: '🏘️ 房源管理',
    color: '#67C23A',
    features: [
      {
        title: '房源发布',
        description: '信息填写 → 图片上传 → 状态管理',
        icon: 'House',
        route: '/landlord/houses/create'
      },
      {
        title: '房源搜索',
        description: '条件筛选 → 结果展示 → 详情查看',
        icon: 'Search',
        route: '/houses'
      },
      {
        title: '房源审核',
        description: '管理员验证 → 状态更新',
        icon: 'Select',
        route: '/admin/houses'
      }
    ]
  },
  {
    category: '📋 租赁流程',
    color: '#E6A23C',
    features: [
      {
        title: '预约看房',
        description: '时间选择 → 房东确认 → 状态跟踪',
        icon: 'Calendar',
        route: '/tenant/appointments'
      },
      {
        title: '合同签署',
        description: '条款确认 → 电子签名 → 生效执行',
        icon: 'Document',
        route: '/tenant/contracts'
      },
      {
        title: '租金支付',
        description: '账单生成 → 支付处理 → 记录管理',
        icon: 'Money',
        route: '/tenant/payments'
      }
    ]
  },
  {
    category: '🔧 维修管理',
    color: '#F56C6C',
    features: [
      {
        title: '维修申请',
        description: '问题描述 → 图片上传 → 紧急程度',
        icon: 'Tools',
        route: '/tenant/repairs/create'
      },
      {
        title: '维修处理',
        description: '房东响应 → 状态更新 → 完成确认',
        icon: 'Setting',
        route: '/landlord/repairs'
      }
    ]
  },
  {
    category: '💬 消息系统',
    color: '#9C27B0',
    features: [
      {
        title: '消息发送',
        description: '用户选择 → 内容编辑 → 即时送达',
        icon: 'ChatDotRound',
        route: '/messages/compose'
      },
      {
        title: '会话管理',
        description: '对话列表 → 历史记录 → 已读标记',
        icon: 'ChatLineRound',
        route: '/messages'
      }
    ]
  }
]

// 跳转到功能页面
const navigateToFeature = (route: string) => {
  router.push(route)
}

// 页面加载时自动测试API连接
onMounted(() => {
  testApiConnection()
})
</script>

<template>
  <main-layout>
    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="floating-elements">
          <div class="floating-house"></div>
          <div class="floating-key"></div>
          <div class="floating-heart"></div>
        </div>
      </div>
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="gradient-text">智能租房</span>
            <br>
            <span class="highlight-text">从未如此简单</span>
          </h1>
          <p class="hero-subtitle">
            🏠 海量优质房源 · 🔒 安全可靠保障 · ⚡ 智能匹配推荐
          </p>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">优质房源</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50,000+</div>
              <div class="stat-label">满意用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">服务满意度</div>
            </div>
          </div>
        </div>
        <div class="hero-search">
          <div class="search-container">
            <div class="search-header">
              <h3>🔍 开始寻找您的理想住所</h3>
            </div>
            <div class="search-form">
              <el-input
                placeholder="🏙️ 输入城市、区域或小区名称"
                class="search-input"
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon class="search-icon"><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" size="large" class="search-btn" @click="viewAllHouses">
                <el-icon><Search /></el-icon>
                立即搜索
              </el-button>
            </div>
            <div class="quick-filters">
              <el-tag class="filter-tag" @click="viewAllHouses">整租</el-tag>
              <el-tag class="filter-tag" @click="viewAllHouses">合租</el-tag>
              <el-tag class="filter-tag" @click="viewAllHouses">公寓</el-tag>
              <el-tag class="filter-tag" @click="viewAllHouses">别墅</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- API状态检测 -->
    <div class="status-section">
      <div class="container">
        <div class="status-card">
          <div class="status-header">
            <div class="status-icon">
              <el-icon size="24" :class="apiStatus === '连接成功' ? 'success' : 'warning'">
                <component :is="apiStatus === '连接成功' ? 'CircleCheck' : 'Warning'" />
              </el-icon>
            </div>
            <div class="status-info">
              <h4>系统状态监控</h4>
              <p>实时监控后端API服务状态</p>
            </div>
          </div>
          <div class="status-body">
            <div class="status-indicator">
              <div class="indicator-item">
                <span class="indicator-label">API连接</span>
                <el-tag
                  :type="apiStatus === '连接成功' ? 'success' : apiStatus === '连接失败' ? 'danger' : 'warning'"
                  effect="dark"
                  size="large"
                >
                  {{ apiStatus }}
                </el-tag>
              </div>
              <div class="indicator-item">
                <span class="indicator-label">服务地址</span>
                <code class="api-url">{{ $env?.VITE_API_BASE_URL || 'http://127.0.0.1:5000/api' }}</code>
              </div>
            </div>
            <div class="status-actions">
              <el-button
                type="primary"
                :loading="loading"
                @click="testApiConnection"
                :icon="loading ? 'Loading' : 'Refresh'"
              >
                {{ loading ? '测试中...' : '重新测试' }}
              </el-button>
              <el-button
                type="info"
                @click="router.push('/debug')"
                :icon="'Tools'"
              >
                调试工具
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 特色房源 -->
    <div class="featured-section" v-if="featuredHouses.length > 0">
      <div class="container">
        <div class="section-header">
          <div class="header-content">
            <h2 class="section-title">
              <span class="title-icon">🏠</span>
              精选优质房源
            </h2>
            <p class="section-subtitle">为您精心挑选的热门房源</p>
          </div>
          <el-button type="primary" plain @click="viewAllHouses" class="view-all-btn">
            查看全部房源
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <div class="houses-grid">
          <div
            v-for="house in featuredHouses"
            :key="house.id"
            class="house-card-modern"
            @click="viewHouseDetail(house.id)"
          >
            <div class="house-image-container">
              <img
                :src="house.main_image_url || 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2'"
                :alt="house.title"
                class="house-image"
              >
              <div class="house-overlay">
                <div class="house-price">¥{{ house.price_monthly || house.monthly_rent }}/月</div>
                <div class="house-status">
                  <el-tag :type="house.status === '可租' ? 'success' : 'warning'" size="small">
                    {{ house.status || '可租' }}
                  </el-tag>
                </div>
              </div>
              <div class="house-favorite">
                <el-icon><Star /></el-icon>
              </div>
            </div>
            <div class="house-content">
              <h3 class="house-title">{{ house.title }}</h3>
              <p class="house-address">
                <el-icon><Location /></el-icon>
                {{ house.address }}
              </p>
              <div class="house-features">
                <span class="feature-item">
                  <el-icon><House /></el-icon>
                  {{ house.bedrooms || 2 }}室{{ house.bathrooms || 1 }}卫
                </span>
                <span class="feature-item">
                  <el-icon><Expand /></el-icon>
                  {{ house.area_sqm || house.area || 80 }}㎡
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统功能流程 -->
    <div class="features-section">
      <div class="container">
        <div class="section-header">
          <div class="header-content">
            <h2 class="section-title">
              <span class="title-icon">🚀</span>
              完整功能生态
            </h2>
            <p class="section-subtitle">智能房屋租赁系统的核心业务流程，一站式解决您的租房需求</p>
          </div>
        </div>

        <div class="features-grid">
          <div
            v-for="(category, index) in systemFeatures"
            :key="index"
            class="feature-category-modern"
          >
            <div class="category-header-modern" :style="{ '--category-color': category.color }">
              <div class="category-icon">
                <span>{{ category.category.split(' ')[0] }}</span>
              </div>
              <div class="category-info">
                <h3>{{ category.category.split(' ')[1] }}</h3>
                <div class="category-line"></div>
              </div>
            </div>

            <div class="features-list">
              <div
                v-for="feature in category.features"
                :key="feature.title"
                class="feature-item-modern"
                @click="navigateToFeature(feature.route)"
              >
                <div class="feature-icon-container" :style="{ '--feature-color': category.color }">
                  <el-icon size="20">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
                <div class="feature-info">
                  <h4 class="feature-title-modern">{{ feature.title }}</h4>
                  <p class="feature-description-modern">{{ feature.description }}</p>
                </div>
                <div class="feature-action">
                  <el-icon class="action-icon" :style="{ color: category.color }">
                    <ArrowRight />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav-section">
      <div class="container">
        <div class="section-header">
          <div class="header-content">
            <h2 class="section-title">
              <span class="title-icon">🎯</span>
              快速开始
            </h2>
            <p class="section-subtitle">选择您的身份，开启租房之旅</p>
          </div>
        </div>
        <div class="quick-nav-grid">
          <div class="nav-card-modern" @click="viewAllHouses">
            <div class="nav-icon-container">
              <el-icon class="nav-icon-modern"><House /></el-icon>
            </div>
            <div class="nav-content-modern">
              <h3>浏览房源</h3>
              <p>查看所有可租房源</p>
            </div>
            <div class="nav-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="nav-card-modern" @click="goToLogin">
            <div class="nav-icon-container">
              <el-icon class="nav-icon-modern"><User /></el-icon>
            </div>
            <div class="nav-content-modern">
              <h3>用户登录</h3>
              <p>登录您的账户</p>
            </div>
            <div class="nav-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="nav-card-modern" @click="goToRegister">
            <div class="nav-icon-container">
              <el-icon class="nav-icon-modern"><UserFilled /></el-icon>
            </div>
            <div class="nav-content-modern">
              <h3>用户注册</h3>
              <p>创建新账户</p>
            </div>
            <div class="nav-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>

          <div class="nav-card-modern" @click="router.push('/debug')">
            <div class="nav-icon-container">
              <el-icon class="nav-icon-modern"><Tools /></el-icon>
            </div>
            <div class="nav-content-modern">
              <h3>调试工具</h3>
              <p>API测试和诊断</p>
            </div>
            <div class="nav-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务优势 -->
    <div class="advantages-section">
      <div class="container">
        <div class="section-header">
          <div class="header-content">
            <h2 class="section-title">
              <span class="title-icon">✨</span>
              为什么选择我们
            </h2>
            <p class="section-subtitle">专业、安全、高效的租房服务体验</p>
          </div>
        </div>
        <div class="advantages-grid">
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="advantage-content">
              <h3>海量优质房源</h3>
              <p>10,000+ 精选房源，覆盖全城热门区域，总有一款适合您</p>
              <ul class="advantage-features">
                <li>✓ 实地认证</li>
                <li>✓ 图片真实</li>
                <li>✓ 价格透明</li>
              </ul>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><Shield /></el-icon>
            </div>
            <div class="advantage-content">
              <h3>安全可靠保障</h3>
              <p>严格的房源审核机制，完善的法律保障，让您租房无忧</p>
              <ul class="advantage-features">
                <li>✓ 身份验证</li>
                <li>✓ 合同保障</li>
                <li>✓ 资金安全</li>
              </ul>
            </div>
          </div>

          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><Service /></el-icon>
            </div>
            <div class="advantage-content">
              <h3>贴心专业服务</h3>
              <p>7×24小时客服支持，从看房到入住全程陪伴服务</p>
              <ul class="advantage-features">
                <li>✓ 专业顾问</li>
                <li>✓ 快速响应</li>
                <li>✓ 售后保障</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main-layout>
</template>

<style scoped>
/* 全局容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-house,
.floating-key,
.floating-heart {
  position: absolute;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.floating-house {
  top: 20%;
  left: 10%;
  font-size: 60px;
  animation-delay: 0s;
}

.floating-key {
  top: 60%;
  right: 15%;
  font-size: 40px;
  animation-delay: 2s;
}

.floating-heart {
  bottom: 30%;
  left: 20%;
  font-size: 50px;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 24px;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.highlight-text {
  color: #fbbf24;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 32px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  gap: 32px;
  margin-bottom: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}

.hero-search {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.search-container {
  width: 100%;
}

.search-header h3 {
  margin: 0 0 24px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.search-form {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
}

.search-btn {
  padding: 0 24px;
  border-radius: 12px;
  font-weight: 600;
}

.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20px;
  padding: 8px 16px;
}

.filter-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 状态检测区域 */
.status-section {
  padding: 60px 0;
  background: #f8fafc;
}

.status-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.status-icon.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-info h4 {
  margin: 0 0 4px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.status-info p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.status-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.status-indicator {
  display: flex;
  gap: 32px;
}

.indicator-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.indicator-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.api-url {
  background: #f1f5f9;
  padding: 8px 12px;
  border-radius: 8px;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.875rem;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.status-actions {
  display: flex;
  gap: 12px;
}

/* 通用区域样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48px;
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 2rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

.view-all-btn {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
}

/* 特色房源区域 */
.featured-section {
  padding: 80px 0;
  background: white;
}

.houses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
}

.house-card-modern {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  cursor: pointer;
  border: 1px solid #f1f5f9;
}

.house-card-modern:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.house-image-container {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.house-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.house-card-modern:hover .house-image {
  transform: scale(1.05);
}

.house-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.house-price {
  background: rgba(255, 255, 255, 0.95);
  color: #1f2937;
  padding: 8px 16px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1.125rem;
  backdrop-filter: blur(10px);
}

.house-status {
  opacity: 0.9;
}

.house-favorite {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.house-favorite:hover {
  background: #fbbf24;
  color: white;
  transform: scale(1.1);
}

.house-content {
  padding: 24px;
}

.house-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.house-address {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 16px;
}

.house-features {
  display: flex;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 0.875rem;
}

/* 功能流程区域 */
.features-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.feature-category-modern {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.category-header-modern {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--category-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.category-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.category-line {
  width: 40px;
  height: 3px;
  background: var(--category-color);
  border-radius: 2px;
  margin-top: 8px;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item-modern {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.feature-item-modern:hover {
  background: #f8fafc;
  border-color: var(--feature-color);
  transform: translateX(8px);
}

.feature-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(64, 158, 255, 0.1);
  color: var(--feature-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-info {
  flex: 1;
}

.feature-title-modern {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.feature-description-modern {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.feature-action {
  opacity: 0;
  transition: all 0.3s ease;
}

.feature-item-modern:hover .feature-action {
  opacity: 1;
}

.action-icon {
  font-size: 1.25rem;
}

/* 快速导航区域 */
.quick-nav-section {
  padding: 80px 0;
  background: white;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.nav-card-modern {
  background: white;
  border: 2px solid #f1f5f9;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card-modern:hover {
  border-color: #3b82f6;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.nav-icon-container {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.nav-icon-modern {
  font-size: 1.5rem;
}

.nav-content-modern {
  flex: 1;
}

.nav-content-modern h3 {
  margin: 0 0 4px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

.nav-content-modern p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.nav-arrow {
  opacity: 0;
  transition: all 0.3s ease;
  color: #3b82f6;
}

.nav-card-modern:hover .nav-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* 服务优势区域 */
.advantages-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.advantage-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
}

.advantage-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-8px);
}

.advantage-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
  margin-bottom: 24px;
}

.advantage-content h3 {
  margin: 0 0 16px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.advantage-content p {
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.advantage-features {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.advantage-features li {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .houses-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding: 40px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .hero-search {
    padding: 24px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-btn {
    width: 100%;
  }

  .status-section {
    padding: 40px 0;
  }

  .status-card {
    padding: 24px;
  }

  .status-body {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .status-indicator {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .status-actions {
    width: 100%;
    justify-content: stretch;
  }

  .status-actions .el-button {
    flex: 1;
  }

  .featured-section,
  .features-section,
  .quick-nav-section,
  .advantages-section {
    padding: 60px 0;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .houses-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .quick-nav-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .nav-card-modern {
    padding: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .feature-category-modern {
    padding: 24px;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .advantage-card {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-search {
    padding: 20px;
  }

  .search-header h3 {
    font-size: 1rem;
  }

  .status-card {
    padding: 20px;
  }

  .status-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .title-icon {
    font-size: 1.5rem;
  }

  .house-card-modern {
    margin: 0 -4px;
  }

  .house-content {
    padding: 20px;
  }

  .nav-card-modern {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .nav-arrow {
    display: none;
  }

  .feature-category-modern {
    padding: 20px;
  }

  .category-header-modern {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .feature-item-modern {
    padding: 12px;
  }

  .advantage-card {
    padding: 20px;
  }

  .advantage-icon {
    width: 56px;
    height: 56px;
    font-size: 1.5rem;
  }
}

/* 动画增强 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.house-card-modern,
.nav-card-modern,
.feature-category-modern,
.advantage-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
