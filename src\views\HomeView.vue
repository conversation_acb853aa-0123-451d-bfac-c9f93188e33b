<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)

// 跳转到房源列表页
const viewAllHouses = () => {
  router.push('/houses')
}
</script>

<template>
  <div class="home-container">
    <div class="header">
      <h1>智能房屋租赁系统</h1>
      <p>欢迎来到智能房屋租赁系统</p>
    </div>

    <div class="content">
      <el-button type="primary" @click="viewAllHouses">查看房源列表</el-button>
      <el-button @click="$router.push('/login')">登录</el-button>
      <el-button @click="$router.push('/register')">注册</el-button>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.header h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.header p {
  font-size: 20px;
}

.content {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.content .el-button {
  min-width: 120px;
}
</style>
