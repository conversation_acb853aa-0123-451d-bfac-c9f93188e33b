<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()
const featuredHouses = ref<HouseListItem[]>([])
const loading = ref(true)

// 获取推荐房源
const fetchFeaturedHouses = async () => {
  try {
    loading.value = true
    const response = await houseService.getHouseList({ per_page: 6 })
    featuredHouses.value = response.items
  } catch (error) {
    console.error('Failed to fetch featured houses:', error)
  } finally {
    loading.value = false
  }
}

// 跳转到房源详情页
const viewHouseDetail = (id: number) => {
  router.push(`/houses/${id}`)
}

// 跳转到房源列表页
const viewAllHouses = () => {
  router.push('/houses')
}

onMounted(() => {
  fetchFeaturedHouses()
})
</script>

<template>
  <main-layout>
    <!-- 首页横幅 -->
    <div class="banner">
      <div class="banner-content">
        <h1>找到您理想的住所</h1>
        <p>智能房屋租赁系统，让租房变得简单、高效、安心</p>
        <div class="search-box">
          <el-input
            placeholder="输入城市、区域或小区名称"
            class="search-input"
            clearable
          >
            <template #append>
              <el-button type="primary" @click="viewAllHouses">
                <el-icon><search /></el-icon>
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 特色房源 -->
    <div class="section">
      <div class="section-header">
        <h2>特色房源</h2>
        <el-button type="text" @click="viewAllHouses">查看全部</el-button>
      </div>
      <el-row :gutter="20" v-loading="loading">
        <el-col :xs="24" :sm="12" :md="8" v-for="house in featuredHouses" :key="house.id">
          <el-card class="house-card" @click="viewHouseDetail(house.id)">
            <div class="house-image">
              <img :src="house.main_image_url || 'https://via.placeholder.com/300x200'" :alt="house.title">
              <div class="house-price">¥{{ house.price_monthly }}/月</div>
            </div>
            <div class="house-info">
              <h3 class="house-title">{{ house.title }}</h3>
              <p class="house-address">{{ house.address }}</p>
              <div class="house-tags">
                <el-tag size="small">{{ house.bedrooms }}室{{ house.bathrooms }}卫</el-tag>
                <el-tag size="small">{{ house.area_sqm }}㎡</el-tag>
                <el-tag size="small" type="success">{{ house.status }}</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务特点 -->
    <div class="section bg-light">
      <h2 class="section-title text-center">我们的服务</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><house /></el-icon>
            <h3>丰富的房源</h3>
            <p>提供各类型、各价位的优质房源，满足不同需求</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><document-checked /></el-icon>
            <h3>安全保障</h3>
            <p>所有房源经过严格审核，确保真实可靠</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><service /></el-icon>
            <h3>贴心服务</h3>
            <p>提供从看房到签约的全流程服务支持</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </main-layout>
</template>

<style scoped>
.banner {
  height: 500px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 40px;
  border-radius: 8px;
}

.banner-content {
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.banner h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.banner p {
  font-size: 20px;
  margin-bottom: 30px;
}

.search-box {
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.section {
  margin-bottom: 40px;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 30px;
}

.house-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.house-card:hover {
  transform: translateY(-5px);
}

.house-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-price {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--el-color-primary);
  color: white;
  padding: 5px 10px;
  font-weight: bold;
}

.house-info {
  padding: 15px;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-tags {
  display: flex;
  gap: 5px;
}

.bg-light {
  background-color: #f5f7fa;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}
</style>
