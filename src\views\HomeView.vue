<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import MainLayout from '@/components/layout/MainLayout.vue'
import api from '@/services/api'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()
const loading = ref(false)
const apiStatus = ref('未测试')
const featuredHouses = ref<HouseListItem[]>([])

// 测试API连接
const testApiConnection = async () => {
  try {
    loading.value = true
    apiStatus.value = '测试中...'

    // 使用实际存在的房源列表接口来测试连接
    const response = await api.get('/houses')

    apiStatus.value = '连接成功'
    ElMessage.success('后端API连接成功！')
    console.log('API响应:', response.data)

    // 连接成功后，直接使用获取到的房源数据
    if (response.data && Array.isArray(response.data)) {
      featuredHouses.value = response.data.slice(0, 6) // 取前6个作为特色房源
      ElMessage.success(`成功获取 ${response.data.length} 个房源`)
    }
  } catch (error) {
    apiStatus.value = '连接失败'
    ElMessage.error('后端API连接失败，请检查Flask服务是否启动在 http://127.0.0.1:5000')
    console.error('API连接错误:', error)
  } finally {
    loading.value = false
  }
}

// 获取推荐房源
const fetchFeaturedHouses = async () => {
  try {
    const response = await houseService.getHouseList()
    featuredHouses.value = response.slice(0, 6) // 取前6个作为特色房源
    ElMessage.success(`成功获取 ${response.length} 个房源`)
  } catch (error) {
    console.error('获取房源失败:', error)
    ElMessage.warning('获取房源数据失败，可能是后端API尚未实现')
  }
}

// 跳转到房源详情页
const viewHouseDetail = (id: number) => {
  router.push(`/houses/${id}`)
}

// 跳转到房源列表页
const viewAllHouses = () => {
  router.push('/houses')
}

// 跳转到登录页
const goToLogin = () => {
  router.push('/login')
}

// 跳转到注册页
const goToRegister = () => {
  router.push('/register')
}

// 系统功能流程数据
const systemFeatures = [
  {
    category: '🔐 认证系统',
    color: '#409EFF',
    features: [
      {
        title: '用户注册',
        description: '角色选择 → 自动跳转专属仪表板',
        icon: 'UserFilled',
        route: '/register'
      },
      {
        title: '用户登录',
        description: '身份验证 → 根据角色显示对应功能',
        icon: 'User',
        route: '/login'
      },
      {
        title: '安全设置',
        description: '密码修改 → 安全验证 → MFA设置',
        icon: 'Lock',
        route: '/profile/security'
      }
    ]
  },
  {
    category: '🏘️ 房源管理',
    color: '#67C23A',
    features: [
      {
        title: '房源发布',
        description: '信息填写 → 图片上传 → 状态管理',
        icon: 'House',
        route: '/landlord/houses/create'
      },
      {
        title: '房源搜索',
        description: '条件筛选 → 结果展示 → 详情查看',
        icon: 'Search',
        route: '/houses'
      },
      {
        title: '房源审核',
        description: '管理员验证 → 状态更新',
        icon: 'Select',
        route: '/admin/houses'
      }
    ]
  },
  {
    category: '📋 租赁流程',
    color: '#E6A23C',
    features: [
      {
        title: '预约看房',
        description: '时间选择 → 房东确认 → 状态跟踪',
        icon: 'Calendar',
        route: '/tenant/appointments'
      },
      {
        title: '合同签署',
        description: '条款确认 → 电子签名 → 生效执行',
        icon: 'Document',
        route: '/tenant/contracts'
      },
      {
        title: '租金支付',
        description: '账单生成 → 支付处理 → 记录管理',
        icon: 'Money',
        route: '/tenant/payments'
      }
    ]
  },
  {
    category: '🔧 维修管理',
    color: '#F56C6C',
    features: [
      {
        title: '维修申请',
        description: '问题描述 → 图片上传 → 紧急程度',
        icon: 'Tools',
        route: '/tenant/repairs/create'
      },
      {
        title: '维修处理',
        description: '房东响应 → 状态更新 → 完成确认',
        icon: 'Setting',
        route: '/landlord/repairs'
      }
    ]
  },
  {
    category: '💬 消息系统',
    color: '#9C27B0',
    features: [
      {
        title: '消息发送',
        description: '用户选择 → 内容编辑 → 即时送达',
        icon: 'ChatDotRound',
        route: '/messages/compose'
      },
      {
        title: '会话管理',
        description: '对话列表 → 历史记录 → 已读标记',
        icon: 'ChatLineRound',
        route: '/messages'
      }
    ]
  }
]

// 跳转到功能页面
const navigateToFeature = (route: string) => {
  router.push(route)
}

// 页面加载时自动测试API连接
onMounted(() => {
  testApiConnection()
})
</script>

<template>
  <main-layout>
    <!-- 首页横幅 -->
    <div class="banner">
      <div class="banner-content">
        <h1>找到您理想的住所</h1>
        <p>智能房屋租赁系统，让租房变得简单、高效、安心</p>
        <div class="search-box">
          <el-input
            placeholder="输入城市、区域或小区名称"
            class="search-input"
            clearable
          >
            <template #append>
              <el-button type="primary" @click="viewAllHouses">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- API状态检测 -->
    <div class="section">
      <div class="api-status">
        <h3>🔗 后端API连接状态</h3>
        <div class="status-content">
          <el-tag
            :type="apiStatus === '连接成功' ? 'success' : apiStatus === '连接失败' ? 'danger' : 'info'"
            size="large"
          >
            {{ apiStatus }}
          </el-tag>
          <el-button
            type="primary"
            size="small"
            :loading="loading"
            @click="testApiConnection"
            style="margin-left: 10px;"
          >
            重新测试连接
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="router.push('/debug')"
            style="margin-left: 10px;"
          >
            调试工具
          </el-button>
        </div>
        <p class="api-info">
          后端地址: <code>{{ $env?.VITE_API_BASE_URL || 'http://127.0.0.1:5000/api' }}</code>
        </p>
      </div>
    </div>

    <!-- 特色房源 -->
    <div class="section" v-if="featuredHouses.length > 0">
      <div class="section-header">
        <h2>🏠 特色房源</h2>
        <el-button type="text" @click="viewAllHouses">查看全部</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="house in featuredHouses" :key="house.id">
          <el-card class="house-card" @click="viewHouseDetail(house.id)">
            <div class="house-image">
              <img :src="house.main_image_url || 'https://via.placeholder.com/300x200'" :alt="house.title">
              <div class="house-price">¥{{ house.price_monthly }}/月</div>
            </div>
            <div class="house-info">
              <h3 class="house-title">{{ house.title }}</h3>
              <p class="house-address">{{ house.address }}</p>
              <div class="house-tags">
                <el-tag size="small">{{ house.bedrooms }}室{{ house.bathrooms }}卫</el-tag>
                <el-tag size="small">{{ house.area_sqm }}㎡</el-tag>
                <el-tag size="small" type="success">{{ house.status }}</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系统功能流程 -->
    <div class="section">
      <div class="section-header">
        <h2>🚀 完整功能流程</h2>
        <p class="section-subtitle">智能房屋租赁系统的核心业务流程</p>
      </div>

      <div class="features-container">
        <div
          v-for="(category, index) in systemFeatures"
          :key="index"
          class="feature-category"
        >
          <div class="category-header" :style="{ borderLeftColor: category.color }">
            <h3>{{ category.category }}</h3>
          </div>

          <el-row :gutter="16" class="feature-row">
            <el-col
              :xs="24"
              :sm="category.features.length === 2 ? 12 : 8"
              :md="category.features.length === 2 ? 12 : 8"
              v-for="feature in category.features"
              :key="feature.title"
            >
              <el-card
                class="feature-card"
                shadow="hover"
                @click="navigateToFeature(feature.route)"
              >
                <div class="feature-content">
                  <div class="feature-icon" :style="{ color: category.color }">
                    <el-icon size="32">
                      <component :is="feature.icon" />
                    </el-icon>
                  </div>
                  <h4 class="feature-title">{{ feature.title }}</h4>
                  <p class="feature-description">{{ feature.description }}</p>
                  <div class="feature-arrow" :style="{ color: category.color }">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="section">
      <div class="section-header">
        <h2>🎯 快速开始</h2>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="6">
          <el-card class="nav-card" @click="viewAllHouses">
            <div class="nav-content">
              <el-icon class="nav-icon"><House /></el-icon>
              <h3>浏览房源</h3>
              <p>查看所有可租房源</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="nav-card" @click="goToLogin">
            <div class="nav-content">
              <el-icon class="nav-icon"><User /></el-icon>
              <h3>用户登录</h3>
              <p>登录您的账户</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="nav-card" @click="goToRegister">
            <div class="nav-content">
              <el-icon class="nav-icon"><UserFilled /></el-icon>
              <h3>用户注册</h3>
              <p>创建新账户</p>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="6">
          <el-card class="nav-card" @click="router.push('/debug')">
            <div class="nav-content">
              <el-icon class="nav-icon"><Tools /></el-icon>
              <h3>调试工具</h3>
              <p>API测试和诊断</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务特点 -->
    <div class="section bg-light">
      <h2 class="section-title text-center">✨ 我们的服务</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><House /></el-icon>
            <h3>丰富的房源</h3>
            <p>提供各类型、各价位的优质房源，满足不同需求</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><DocumentChecked /></el-icon>
            <h3>安全保障</h3>
            <p>所有房源经过严格审核，确保真实可靠</p>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <div class="feature-card">
            <el-icon class="feature-icon"><Service /></el-icon>
            <h3>贴心服务</h3>
            <p>提供从看房到签约的全流程服务支持</p>
          </div>
        </el-col>
      </el-row>
    </div>
  </main-layout>
</template>

<style scoped>
.banner {
  height: 500px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1560448204-e02f11c3d0e2');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 40px;
  border-radius: 8px;
}

.banner-content {
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.banner h1 {
  font-size: 48px;
  margin-bottom: 20px;
}

.banner p {
  font-size: 20px;
  margin-bottom: 30px;
}

.search-box {
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
}

.section {
  margin-bottom: 40px;
  padding: 20px;
  border-radius: 8px;
  background-color: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 30px;
}

.api-status {
  text-align: center;
  padding: 20px;
}

.api-status h3 {
  margin-bottom: 15px;
  color: #303133;
}

.status-content {
  margin-bottom: 10px;
}

.api-info {
  margin: 10px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.api-info code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.house-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.house-card:hover {
  transform: translateY(-5px);
}

.house-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-price {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--el-color-primary);
  color: white;
  padding: 5px 10px;
  font-weight: bold;
}

.house-info {
  padding: 15px;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-tags {
  display: flex;
  gap: 5px;
}

.nav-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.nav-card:hover {
  transform: translateY(-5px);
}

.nav-content {
  text-align: center;
  padding: 20px;
}

.nav-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 15px;
}

.nav-content h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.nav-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.bg-light {
  background-color: #f5f7fa;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

/* 系统功能流程样式 */
.section-subtitle {
  color: #909399;
  font-size: 16px;
  margin: 8px 0 0 0;
  font-weight: normal;
}

.features-container {
  margin-top: 30px;
}

.feature-category {
  margin-bottom: 40px;
}

.category-header {
  border-left: 4px solid #409EFF;
  padding-left: 16px;
  margin-bottom: 20px;
  background: linear-gradient(90deg, rgba(64, 158, 255, 0.1) 0%, transparent 100%);
  padding: 12px 16px;
  border-radius: 0 8px 8px 0;
}

.category-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.feature-row {
  margin-bottom: 20px;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #EBEEF5;
  margin-bottom: 16px;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.feature-content {
  padding: 20px;
  text-align: center;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feature-icon {
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.feature-description {
  margin: 0 0 16px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  flex-grow: 1;
}

.feature-arrow {
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 16px;
}

.feature-card:hover .feature-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .banner h1 {
    font-size: 32px;
  }

  .banner p {
    font-size: 16px;
  }

  .category-header {
    padding: 8px 12px;
  }

  .category-header h3 {
    font-size: 18px;
  }

  .feature-content {
    padding: 16px;
  }

  .feature-title {
    font-size: 15px;
  }

  .feature-description {
    font-size: 13px;
  }

  .nav-icon {
    font-size: 36px;
  }

  .nav-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .banner {
    height: 400px;
  }

  .banner h1 {
    font-size: 28px;
  }

  .banner p {
    font-size: 14px;
  }

  .section {
    padding: 16px;
  }

  .feature-content {
    padding: 12px;
  }
}
</style>
