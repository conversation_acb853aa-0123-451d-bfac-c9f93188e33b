<template>
  <el-header class="app-header">
    <div class="logo-container">
      <router-link to="/">
        <h1 class="logo">智能房屋租赁系统</h1>
      </router-link>
    </div>
    <div class="menu-container">
      <el-menu
        mode="horizontal"
        :router="true"
        :default-active="activeIndex"
        class="app-menu"
      >
        <el-menu-item index="/">首页</el-menu-item>
        <el-menu-item index="/houses">房源列表</el-menu-item>
      </el-menu>
    </div>
    <div class="user-container">
      <template v-if="isLoggedIn">
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="user-dropdown">
            {{ username }}
            <el-icon><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="isLandlord" command="landlord">房东中心</el-dropdown-item>
              <el-dropdown-item v-if="isTenant" command="tenant">租客中心</el-dropdown-item>
              <el-dropdown-item v-if="isAdmin" command="admin">管理中心</el-dropdown-item>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-else>
        <router-link to="/login">
          <el-button type="primary">登录</el-button>
        </router-link>
        <router-link to="/register">
          <el-button>注册</el-button>
        </router-link>
      </template>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 计算当前激活的菜单项
const activeIndex = computed(() => route.path)

// 从认证状态获取用户信息
const isLoggedIn = computed(() => authStore.isLoggedIn)
const username = computed(() => authStore.user?.username || '')
const isLandlord = computed(() => authStore.isLandlord)
const isTenant = computed(() => authStore.isTenant)
const isAdmin = computed(() => authStore.isAdmin)

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'landlord':
      router.push('/landlord/dashboard')
      break
    case 'tenant':
      router.push('/tenant/dashboard')
      break
    case 'admin':
      router.push('/admin/dashboard')
      break
    case 'profile':
      if (isLandlord.value) {
        router.push('/landlord/profile')
      } else if (isTenant.value) {
        router.push('/tenant/profile')
      } else if (isAdmin.value) {
        router.push('/admin/profile')
      }
      break
    case 'logout':
      authStore.logout()
      break
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.logo-container {
  flex: 0 0 auto;
}

.logo {
  margin: 0;
  font-size: 20px;
  color: var(--el-color-primary);
}

.menu-container {
  flex: 1 1 auto;
  display: flex;
  justify-content: center;
}

.app-menu {
  border-bottom: none;
}

.user-container {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--el-color-primary);
}

.user-dropdown .el-icon {
  margin-left: 5px;
}
</style>
