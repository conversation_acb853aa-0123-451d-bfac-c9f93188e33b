import api from './api'
import type { PaginatedResponse } from './user.service'

export interface Appointment {
  id: number
  house: {
    id: number
    title: string
    address: string
    main_image_url: string
  }
  tenant: {
    id: number
    username: string
    full_name: string
  }
  appointment_time: string
  status: string
  message?: string
  created_at: string
}

export interface CreateAppointmentRequest {
  house_id: number
  appointment_time: string
  message?: string
}

export interface CancelAppointmentRequest {
  reason: string
}

export interface MessageResponse {
  message: string
}

class AppointmentService {
  /**
   * 获取预约列表
   * @param params 查询参数
   */
  async getAppointmentList(params?: {
    status?: string
    house_id?: number
  }): Promise<Appointment[]> {
    const response = await api.get<Appointment[]>('/leases/appointments', { params })
    return response.data
  }

  /**
   * 创建预约
   * @param data 预约信息
   */
  async createAppointment(data: CreateAppointmentRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>('/leases/appointments', data)
    return response.data
  }

  /**
   * 确认预约 (房东)
   * @param appointmentId 预约ID
   */
  async confirmAppointment(appointmentId: number): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/appointments/${appointmentId}/confirm`)
    return response.data
  }

  /**
   * 取消预约
   * @param appointmentId 预约ID
   * @param data 取消原因
   */
  async cancelAppointment(appointmentId: number, data: CancelAppointmentRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/appointments/${appointmentId}/cancel`, data)
    return response.data
  }

  /**
   * 获取预约详情
   * @param appointmentId 预约ID
   */
  async getAppointmentDetail(appointmentId: number): Promise<Appointment> {
    const response = await api.get<Appointment>(`/leases/appointments/${appointmentId}`)
    return response.data
  }

  /**
   * 更新预约状态
   * @param appointmentId 预约ID
   * @param status 新状态
   */
  async updateAppointmentStatus(appointmentId: number, status: string): Promise<{ id: number; status: string; message: string }> {
    const response = await api.patch<{ id: number; status: string; message: string }>(`/leases/appointments/${appointmentId}/status`, { status })
    return response.data
  }
}

export default new AppointmentService()
