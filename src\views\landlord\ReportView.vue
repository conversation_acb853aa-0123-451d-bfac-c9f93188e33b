<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 报表数据
const reportData = ref({
  overview: {
    totalHouses: 0,
    rentedHouses: 0,
    availableHouses: 0,
    totalIncome: 0,
    monthlyIncome: 0,
    occupancyRate: 0
  },
  monthlyData: [],
  housePerformance: [],
  tenantAnalysis: {
    totalTenants: 0,
    activeTenants: 0,
    averageStayDuration: 0
  }
})

const loading = ref(true)

// 获取报表数据
const fetchReportData = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockData = {
      overview: {
        totalHouses: 12,
        rentedHouses: 10,
        availableHouses: 2,
        totalIncome: 156000,
        monthlyIncome: 35000,
        occupancyRate: 83.3
      },
      monthlyData: [
        { month: '2023-07', income: 32000, houses: 8 },
        { month: '2023-08', income: 33500, houses: 9 },
        { month: '2023-09', income: 34000, houses: 9 },
        { month: '2023-10', income: 34500, houses: 10 },
        { month: '2023-11', income: 35000, houses: 10 },
        { month: '2023-12', income: 35000, houses: 10 }
      ],
      housePerformance: [
        {
          id: 1,
          title: '阳光花园精装两居室',
          address: '朝阳区阳光花园小区',
          monthlyRent: 3500,
          occupancyRate: 100,
          totalIncome: 21000,
          status: '已租'
        },
        {
          id: 2,
          title: '温馨一居室',
          address: '海淀区中关村大街',
          monthlyRent: 2800,
          occupancyRate: 100,
          totalIncome: 16800,
          status: '已租'
        },
        {
          id: 3,
          title: '豪华三居室',
          address: '西城区金融街',
          monthlyRent: 5500,
          occupancyRate: 0,
          totalIncome: 0,
          status: '可租'
        }
      ],
      tenantAnalysis: {
        totalTenants: 15,
        activeTenants: 10,
        averageStayDuration: 18
      }
    }
    
    reportData.value = mockData
  } catch (error) {
    console.error('Failed to fetch report data:', error)
  } finally {
    loading.value = false
  }
}

// 导出报表
const exportReport = () => {
  // 模拟导出功能
  const data = JSON.stringify(reportData.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `房东报表_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
}

onMounted(() => {
  fetchReportData()
})
</script>

<template>
  <div class="report-container" v-loading="loading">
    <div class="page-header">
      <h1>数据报表</h1>
      <el-button type="primary" @click="exportReport">导出报表</el-button>
    </div>
    
    <!-- 概览统计 -->
    <div class="overview-section">
      <h2>概览统计</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon"><house /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.totalHouses }}</div>
                <div class="stat-label">总房源数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon rented-icon"><check /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.rentedHouses }}</div>
                <div class="stat-label">已出租</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon available-icon"><plus /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.availableHouses }}</div>
                <div class="stat-label">可出租</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon income-icon"><money /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.occupancyRate }}%</div>
                <div class="stat-label">出租率</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 收入统计 -->
    <div class="income-section">
      <h2>收入统计</h2>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div class="income-item">
              <span class="income-label">总收入</span>
              <span class="income-value total">¥{{ reportData.overview.totalIncome.toLocaleString() }}</span>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div class="income-item">
              <span class="income-label">本月收入</span>
              <span class="income-value monthly">¥{{ reportData.overview.monthlyIncome.toLocaleString() }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 月度收入趋势 -->
    <div class="trend-section">
      <h2>月度收入趋势</h2>
      <el-card>
        <el-table :data="reportData.monthlyData" style="width: 100%">
          <el-table-column prop="month" label="月份" width="120" />
          <el-table-column prop="income" label="收入(元)" width="150">
            <template #default="{ row }">
              <span class="income-amount">¥{{ row.income.toLocaleString() }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="houses" label="出租房源数" width="150" />
          <el-table-column label="环比增长" width="150">
            <template #default="{ row, $index }">
              <span v-if="$index === 0">-</span>
              <span v-else class="growth-rate">
                {{ ((row.income - reportData.monthlyData[$index - 1].income) / reportData.monthlyData[$index - 1].income * 100).toFixed(1) }}%
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 房源表现 -->
    <div class="performance-section">
      <h2>房源表现</h2>
      <el-card>
        <el-table :data="reportData.housePerformance" style="width: 100%">
          <el-table-column label="房源信息" min-width="200">
            <template #default="{ row }">
              <div>
                <div class="house-title">{{ row.title }}</div>
                <div class="house-address">{{ row.address }}</div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="monthlyRent" label="月租金" width="120">
            <template #default="{ row }">
              <span class="rent-amount">¥{{ row.monthlyRent }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="occupancyRate" label="出租率" width="120">
            <template #default="{ row }">
              <el-tag :type="row.occupancyRate === 100 ? 'success' : 'warning'">
                {{ row.occupancyRate }}%
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="totalIncome" label="总收入" width="150">
            <template #default="{ row }">
              <span class="total-income">¥{{ row.totalIncome.toLocaleString() }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === '已租' ? 'success' : 'info'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 租客分析 -->
    <div class="tenant-section">
      <h2>租客分析</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card>
            <div class="tenant-stat">
              <div class="tenant-value">{{ reportData.tenantAnalysis.totalTenants }}</div>
              <div class="tenant-label">总租客数</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card>
            <div class="tenant-stat">
              <div class="tenant-value">{{ reportData.tenantAnalysis.activeTenants }}</div>
              <div class="tenant-label">活跃租客</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card>
            <div class="tenant-stat">
              <div class="tenant-value">{{ reportData.tenantAnalysis.averageStayDuration }}个月</div>
              <div class="tenant-label">平均租期</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.report-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.overview-section,
.income-section,
.trend-section,
.performance-section,
.tenant-section {
  margin-bottom: 30px;
}

.overview-section h2,
.income-section h2,
.trend-section h2,
.performance-section h2,
.tenant-section h2 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #303133;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.rented-icon {
  color: #67c23a;
}

.available-icon {
  color: #e6a23c;
}

.income-icon {
  color: #f56c6c;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.income-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.income-label {
  font-size: 16px;
  color: #606266;
}

.income-value {
  font-size: 24px;
  font-weight: bold;
}

.income-value.total {
  color: #67c23a;
}

.income-value.monthly {
  color: #409eff;
}

.income-amount,
.rent-amount,
.total-income {
  color: #f56c6c;
  font-weight: bold;
}

.growth-rate {
  color: #67c23a;
  font-weight: bold;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.tenant-stat {
  text-align: center;
  padding: 20px 0;
}

.tenant-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.tenant-label {
  color: #909399;
}
</style>
