<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import leaseService from '@/services/lease.service'
import type { Lease } from '@/services/lease.service'

const router = useRouter()

// 租约列表数据
const leases = ref<Lease[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  keyword: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: '待签署', label: '待签署' },
  { value: '生效中', label: '生效中' },
  { value: '已到期', label: '已到期' },
  { value: '已解除', label: '已解除' }
]

// 获取租约列表
const fetchLeases = async () => {
  try {
    loading.value = true
    
    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await leaseService.getLeaseList(params)
    leases.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch leases:', error)
    ElMessage.error('获取租约列表失败')
  } finally {
    loading.value = false
  }
}

// 强制终止租约
const forceTerminateLease = (leaseId: number) => {
  ElMessageBox.prompt('请输入强制终止原因', '强制终止租约', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入强制终止原因'
  }).then(async ({ value }) => {
    try {
      await leaseService.forceTerminateLease(leaseId, {
        reason: value || '管理员强制终止'
      })
      
      // 更新状态
      const index = leases.value.findIndex(item => item.id === leaseId)
      if (index !== -1) {
        leases.value[index].status = '已解除'
      }
      
      ElMessage.success('租约已强制终止')
    } catch (error) {
      console.error('Failed to force terminate lease:', error)
      ElMessage.error('强制终止租约失败')
    }
  }).catch(() => {})
}

// 删除租约
const deleteLease = (leaseId: number) => {
  ElMessageBox.confirm('确定要删除该租约吗？删除后无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await leaseService.deleteLease(leaseId)
      
      // 从列表中移除
      leases.value = leases.value.filter(item => item.id !== leaseId)
      total.value--
      
      ElMessage.success('租约已删除')
    } catch (error) {
      console.error('Failed to delete lease:', error)
      ElMessage.error('删除租约失败')
    }
  }).catch(() => {})
}

// 查看租约详情
const viewLeaseDetail = (leaseId: number) => {
  router.push(`/admin/leases/${leaseId}`)
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理搜索
const handleSearch = () => {
  filters.page = 1
  fetchLeases()
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    keyword: '',
    page: 1,
    per_page: 10
  })
  
  fetchLeases()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchLeases()
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '生效中': 'success',
    '待签署': 'warning',
    '已到期': 'info',
    '已解除': 'danger'
  }
  return statusMap[status] || 'info'
}

// 计算租约剩余天数
const calculateRemainingDays = (endDate: string) => {
  const end = new Date(endDate)
  const today = new Date()
  const diffTime = end.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

onMounted(() => {
  fetchLeases()
})
</script>

<template>
  <div class="lease-list-container">
    <div class="page-header">
      <h1>租约管理</h1>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索房源、租客、房东"
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="lease-list" v-loading="loading">
      <el-empty v-if="leases.length === 0 && !loading" description="暂无租约记录" />
      
      <el-table :data="leases" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="房源信息" width="200">
          <template #default="{ row }">
            <div class="house-info">
              <div class="house-title" @click="viewHouseDetail(row.house.id)">
                {{ row.house.title }}
              </div>
              <div class="house-address">{{ row.house.address }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租客信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="tenant-name">{{ row.tenant.full_name }}</div>
              <div class="tenant-phone">{{ row.tenant.phone_number }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="房东信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="landlord-name">{{ row.landlord.full_name }}</div>
              <div class="landlord-phone">{{ row.landlord.phone_number }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租期" width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.start_date }} 至</div>
              <div>{{ row.end_date }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="rent_amount_monthly" label="月租金" width="120">
          <template #default="{ row }">
            <span class="price">¥{{ row.rent_amount_monthly }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="剩余天数" width="100">
          <template #default="{ row }">
            <span v-if="row.status === '生效中'">
              {{ calculateRemainingDays(row.end_date) }}天
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleDateString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewLeaseDetail(row.id)"
            >
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              v-if="row.status === '生效中'"
              @click="forceTerminateLease(row.id)"
            >
              强制终止
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteLease(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.lease-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.lease-list {
  min-height: 400px;
}

.house-info {
  display: flex;
  flex-direction: column;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
  cursor: pointer;
  color: #303133;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.tenant-name,
.landlord-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.tenant-phone,
.landlord-phone {
  font-size: 12px;
  color: #909399;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
