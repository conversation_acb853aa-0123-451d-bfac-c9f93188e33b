import api from './api'
import type { PaginatedResponse } from './user.service'

export interface Repair {
  id: number
  house: {
    id: number
    title: string
    address: string
  }
  tenant: {
    id: number
    username: string
    full_name: string
    phone_number: string
  }
  description: string
  urgency_level: string
  images: {
    id: number
    url: string
  }[]
  status: string
  landlord_response: string | null
  completed_at: string | null
  submitted_at: string
  updated_at: string
}

export interface CreateRepairRequest {
  house_id: number
  description: string
  urgency_level: string
}

export interface ProcessRepairRequest {
  landlord_response: string
  estimated_completion_date?: string
}

export interface CompleteRepairRequest {
  completion_notes: string
}

export interface UploadImagesResponse {
  images: {
    id: number
    url: string
  }[]
}

export interface MessageResponse {
  message: string
}

class RepairService {
  /**
   * 获取维修请求列表
   * @param params 查询参数
   */
  async getRepairList(params?: {
    status?: string
    page?: number
    per_page?: number
  }): Promise<PaginatedResponse<Repair>> {
    const response = await api.get<PaginatedResponse<Repair>>('/repairs', { params })
    return response.data
  }

  /**
   * 获取维修请求详情
   * @param repairId 维修请求ID
   */
  async getRepairDetail(repairId: number): Promise<Repair> {
    const response = await api.get<Repair>(`/repairs/${repairId}`)
    return response.data
  }

  /**
   * 创建维修请求 (租客)
   * @param data 维修请求信息
   */
  async createRepair(data: CreateRepairRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>('/repairs', data)
    return response.data
  }

  /**
   * 上传维修请求图片 (租客)
   * @param repairId 维修请求ID
   * @param files 图片文件列表
   */
  async uploadRepairImages(repairId: number, files: File[]): Promise<UploadImagesResponse> {
    const formData = new FormData()

    files.forEach((file, index) => {
      formData.append(`image_${index}`, file)
    })

    const response = await api.post<UploadImagesResponse>(`/repairs/${repairId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  /**
   * 处理维修请求 (房东)
   * @param repairId 维修请求ID
   * @param data 处理信息
   */
  async processRepair(repairId: number, data: ProcessRepairRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/repairs/${repairId}/process`, data)
    return response.data
  }

  /**
   * 完成维修请求 (房东)
   * @param repairId 维修请求ID
   * @param data 完成信息
   */
  async completeRepair(repairId: number, data: CompleteRepairRequest): Promise<{ id: number; status: string; message: string }> {
    const response = await api.post<{ id: number; status: string; message: string }>(`/repairs/${repairId}/complete`, data)
    return response.data
  }
}

export default new RepairService()
