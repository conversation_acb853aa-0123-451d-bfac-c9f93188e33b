<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import userService from '@/services/user.service'
import type { UserListItem } from '@/services/user.service'

// 用户列表数据
const users = ref<UserListItem[]>([])
const loading = ref(true)

// 筛选条件
const filters = ref({
  role: '',
  search: ''
})

// 角色选项
const roleOptions = [
  { label: '全部', value: '' },
  { label: '租客', value: 'tenant' },
  { label: '房东', value: 'landlord' },
  { label: '管理员', value: 'admin' }
]

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const params = Object.entries(filters.value).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await userService.getUserList(params)
    users.value = response
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
    users.value = []
  } finally {
    loading.value = false
  }
}

// 搜索用户
const handleSearch = () => {
  fetchUsers()
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    role: '',
    search: ''
  }
  fetchUsers()
}

// 格式化角色
const formatRole = (role: string) => {
  const roleMap: Record<string, string> = {
    'tenant': '租客',
    'landlord': '房东',
    'admin': '管理员'
  }
  return roleMap[role] || role
}

// 格式化状态
const formatStatus = (status: string) => {
  return status === 'active' ? '正常' : '禁用'
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  return status === 'active' ? 'success' : 'danger'
}

onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统中的所有用户</p>
    </div>
    
    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="6">
          <el-select v-model="filters.role" placeholder="选择角色" clearable>
            <el-option
              v-for="option in roleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        
        <el-col :span="8">
          <el-input
            v-model="filters.search"
            placeholder="搜索用户名或姓名"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        
        <el-col :span="10">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table :data="users" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="full_name" label="真实姓名" width="120" />
        
        <el-table-column prop="email" label="邮箱" width="200" />
        
        <el-table-column prop="phone_number" label="手机号" width="130" />
        
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 'admin' ? 'danger' : row.role === 'landlord' ? 'warning' : 'primary'">
              {{ formatRole(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="注册时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" link>
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" link>
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-empty v-if="!loading && users.length === 0" description="暂无用户数据" />
    </el-card>
  </div>
</template>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  background: white;
}

.el-select,
.el-input {
  width: 100%;
}
</style>
