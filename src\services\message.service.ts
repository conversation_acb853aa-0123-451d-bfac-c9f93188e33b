import api from './api'

export interface Message {
  id: number
  sender: {
    id: number
    username: string
    full_name: string
    avatar_url?: string
  }
  receiver: {
    id: number
    username: string
    full_name: string
    avatar_url?: string
  }
  content: string
  is_read: boolean
  created_at: string
}

export interface SendMessageRequest {
  receiver_id: number
  content: string
}

export interface UnreadCountResponse {
  count: number
}

export interface MessageResponse {
  message: string
}

class MessageService {
  /**
   * 获取消息列表
   * @param params 查询参数
   */
  async getMessageList(params?: {
    type?: 'sent' | 'received'
    is_read?: boolean
  }): Promise<Message[]> {
    const response = await api.get<Message[]>('/messages', { params })
    return response.data
  }

  /**
   * 获取与特定用户的对话
   * @param userId 用户ID
   * @param params 查询参数
   */
  async getConversation(userId: number, params?: {
    page?: number
    per_page?: number
  }): Promise<Message[]> {
    const response = await api.get<Message[]>(`/messages/conversation/${userId}`, { params })
    return response.data
  }

  /**
   * 发送消息
   * @param data 消息信息
   */
  async sendMessage(data: SendMessageRequest): Promise<{ id: number; created_at: string; message: string }> {
    const response = await api.post<{ id: number; created_at: string; message: string }>('/messages', data)
    return response.data
  }

  /**
   * 标记消息为已读
   * @param messageId 消息ID
   */
  async markMessageAsRead(messageId: number): Promise<MessageResponse> {
    const response = await api.put<MessageResponse>(`/messages/${messageId}/read`)
    return response.data
  }

  /**
   * 获取未读消息数量
   */
  async getUnreadCount(): Promise<UnreadCountResponse> {
    const response = await api.get<UnreadCountResponse>('/messages/unread/count')
    return response.data
  }
}

export default new MessageService()
