<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import appointmentService from '@/services/appointment.service'
import type { Appointment } from '@/services/appointment.service'

const router = useRouter()

// 预约列表数据
const appointments = ref<Appointment[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待确认', label: '待确认' },
  { value: '已确认', label: '已确认' },
  { value: '已取消', label: '已取消' },
  { value: '已完成', label: '已完成' }
]

// 获取预约列表
const fetchAppointments = async () => {
  try {
    loading.value = true
    
    const response = await appointmentService.getAppointmentList({
      status: filters.status,
      page: filters.page,
      per_page: filters.per_page
    })
    
    appointments.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch appointments:', error)
    ElMessage.error('获取预约列表失败')
  } finally {
    loading.value = false
  }
}

// 确认预约
const confirmAppointment = async (appointmentId: number) => {
  try {
    await appointmentService.confirmAppointment(appointmentId)
    
    // 更新状态
    const index = appointments.value.findIndex(item => item.id === appointmentId)
    if (index !== -1) {
      appointments.value[index].status = '已确认'
    }
    
    ElMessage.success('预约已确认')
  } catch (error) {
    console.error('Failed to confirm appointment:', error)
    ElMessage.error('确认预约失败')
  }
}

// 取消预约
const cancelAppointment = (appointmentId: number) => {
  ElMessageBox.prompt('请输入取消原因', '取消预约', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入取消原因'
  }).then(async ({ value }) => {
    try {
      await appointmentService.cancelAppointment(appointmentId, {
        reason: value || '房东取消'
      })
      
      // 更新状态
      const index = appointments.value.findIndex(item => item.id === appointmentId)
      if (index !== -1) {
        appointments.value[index].status = '已取消'
      }
      
      ElMessage.success('预约已取消')
    } catch (error) {
      console.error('Failed to cancel appointment:', error)
      ElMessage.error('取消预约失败')
    }
  }).catch(() => {})
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchAppointments()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchAppointments()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchAppointments()
})
</script>

<template>
  <div class="appointment-list-container">
    <div class="page-header">
      <h1>看房预约</h1>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="appointment-list" v-loading="loading">
      <el-empty v-if="appointments.length === 0 && !loading" description="暂无预约记录" />
      
      <el-table :data="appointments" style="width: 100%">
        <el-table-column label="房源信息" width="250">
          <template #default="{ row }">
            <div class="house-info">
              <el-image
                :src="row.house.main_image_url"
                :alt="row.house.title"
                class="house-image"
                fit="cover"
                @click="viewHouseDetail(row.house.id)"
              />
              <div>
                <div class="house-title" @click="viewHouseDetail(row.house.id)">
                  {{ row.house.title }}
                </div>
                <div class="house-address">{{ row.house.address }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租客信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="tenant-name">{{ row.tenant.full_name }}</div>
              <div class="tenant-username">{{ row.tenant.username }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="appointment_time" label="预约时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.appointment_time) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '已确认' ? 'success' :
                row.status === '待确认' ? 'warning' :
                row.status === '已取消' ? 'info' : 'primary'
              "
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="留言" min-width="200">
          <template #default="{ row }">
            <div class="message-text">{{ row.message || '无' }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              v-if="row.status === '待确认'"
              @click="confirmAppointment(row.id)"
            >
              确认
            </el-button>
            <el-button
              type="danger"
              size="small"
              v-if="row.status === '待确认' || row.status === '已确认'"
              @click="cancelAppointment(row.id)"
            >
              取消
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewHouseDetail(row.house.id)"
            >
              查看房源
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.appointment-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.appointment-list {
  min-height: 400px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-image {
  width: 60px;
  height: 40px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
  cursor: pointer;
  color: #303133;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.tenant-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.tenant-username {
  font-size: 12px;
  color: #909399;
}

.message-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
