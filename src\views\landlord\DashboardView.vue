<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import houseService from '@/services/house.service'
import leaseService from '@/services/lease.service'
import appointmentService from '@/services/appointment.service'
import repairService from '@/services/repair.service'

const router = useRouter()
const authStore = useAuthStore()

// 用户信息
const user = ref(authStore.user)

// 统计数据
const stats = ref({
  houseCount: 0,
  appointmentCount: 0,
  leaseCount: 0,
  repairCount: 0,
  monthlyIncome: 0
})

// 最近预约
const recentAppointments = ref([])

// 最近租约
const recentLeases = ref([])

// 待处理维修
const pendingRepairs = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取房源统计
    const houseResponse = await houseService.getHouseList({ per_page: 1 })
    stats.value.houseCount = houseResponse.total

    // 获取预约统计
    const appointmentResponse = await appointmentService.getAppointmentList({ per_page: 1 })
    stats.value.appointmentCount = appointmentResponse.total

    // 获取租约统计
    const leaseResponse = await leaseService.getLeaseList({ per_page: 1 })
    stats.value.leaseCount = leaseResponse.total

    // 获取维修统计
    const repairResponse = await repairService.getRepairList({ status: '待处理', per_page: 1 })
    stats.value.repairCount = repairResponse.total

    // 计算月收入（这里简化处理）
    const activeLeases = await leaseService.getLeaseList({ status: '生效中' })
    stats.value.monthlyIncome = activeLeases.items.reduce((sum, lease) => sum + lease.rent_amount_monthly, 0)
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近预约
const fetchRecentAppointments = async () => {
  try {
    const response = await appointmentService.getAppointmentList({ per_page: 5 })
    recentAppointments.value = response.items
  } catch (error) {
    console.error('Failed to fetch recent appointments:', error)
  }
}

// 获取最近租约
const fetchRecentLeases = async () => {
  try {
    const response = await leaseService.getLeaseList({ per_page: 5 })
    recentLeases.value = response.items
  } catch (error) {
    console.error('Failed to fetch recent leases:', error)
  }
}

// 获取待处理维修
const fetchPendingRepairs = async () => {
  try {
    const response = await repairService.getRepairList({ status: '待处理', per_page: 5 })
    pendingRepairs.value = response.items
  } catch (error) {
    console.error('Failed to fetch pending repairs:', error)
  }
}

// 跳转到详情页
const goToDetail = (path: string) => {
  router.push(path)
}

onMounted(() => {
  fetchStats()
  fetchRecentAppointments()
  fetchRecentLeases()
  fetchPendingRepairs()
})
</script>

<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h1>欢迎回来，{{ user?.username }}</h1>
      <p>这是您的房东控制台，您可以在这里管理您的房源和租约。</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><house /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.houseCount }}</div>
              <div class="stat-label">我的房源</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/landlord/houses')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><calendar /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.appointmentCount }}</div>
              <div class="stat-label">看房预约</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/landlord/appointments')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><document /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.leaseCount }}</div>
              <div class="stat-label">租约管理</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/landlord/leases')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><money /></el-icon>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.monthlyIncome }}</div>
              <div class="stat-label">月收入</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/landlord/payments')">查看详情</el-button>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 待处理维修 -->
    <el-card class="recent-section" v-if="pendingRepairs.length > 0">
      <template #header>
        <div class="card-header">
          <h2>待处理维修</h2>
          <el-button text @click="goToDetail('/landlord/repairs')">查看全部</el-button>
        </div>
      </template>
      
      <el-table :data="pendingRepairs" style="width: 100%">
        <el-table-column label="房源信息">
          <template #default="{ row }">
            <div class="house-info">
              <div class="house-title">{{ row.house.title }}</div>
              <div class="house-address">{{ row.house.address }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="问题描述" width="200">
          <template #default="{ row }">
            <div class="description-text">{{ row.description }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="urgency_level" label="紧急程度" width="120">
          <template #default="{ row }">
            <el-tag
              :type="
                row.urgency_level === '紧急' ? 'danger' :
                row.urgency_level === '一般' ? 'warning' : 'info'
              "
            >
              {{ row.urgency_level }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.submitted_at).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/landlord/repairs/${row.id}`)"
            >
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 最近预约 -->
    <el-card class="recent-section">
      <template #header>
        <div class="card-header">
          <h2>最近预约</h2>
          <el-button text @click="goToDetail('/landlord/appointments')">查看全部</el-button>
        </div>
      </template>
      
      <el-empty v-if="recentAppointments.length === 0" description="暂无预约记录" />
      
      <el-table v-else :data="recentAppointments" style="width: 100%">
        <el-table-column label="房源信息">
          <template #default="{ row }">
            <div class="house-info">
              <div class="house-title">{{ row.house.title }}</div>
              <div class="house-address">{{ row.house.address }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租客信息" width="150">
          <template #default="{ row }">
            <div>{{ row.tenant.full_name }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="appointment_time" label="预约时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.appointment_time).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '已确认' ? 'success' :
                row.status === '待确认' ? 'warning' : 'info'
              "
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/landlord/appointments/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.welcome-section p {
  margin: 0;
  color: #606266;
}

.stat-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.recent-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.house-info {
  display: flex;
  flex-direction: column;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.description-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
</style>
