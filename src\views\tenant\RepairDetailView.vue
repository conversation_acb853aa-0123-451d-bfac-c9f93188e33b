<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

// 维修请求详情
interface RepairDetail {
  id: number
  house: {
    id: number
    title: string
    address: string
  }
  tenant: {
    id: number
    username: string
    full_name: string
    phone_number: string
  }
  description: string
  urgency_level: string
  images: {
    id: number
    url: string
  }[]
  status: string
  landlord_response: string | null
  completed_at: string | null
  submitted_at: string
  updated_at: string
}

const route = useRoute()
const router = useRouter()

// 维修请求ID
const repairId = ref<number>(parseInt(route.params.id as string))

// 维修请求详情
const repair = ref<RepairDetail | null>(null)
const loading = ref(true)

// 当前查看的图片
const currentImage = ref('')
const imageDialogVisible = ref(false)

// 获取维修请求详情
const fetchRepairDetail = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    setTimeout(() => {
      // 模拟数据
      repair.value = {
        id: repairId.value,
        house: {
          id: 1,
          title: '阳光花园精装两居室',
          address: '北京市朝阳区阳光花园小区'
        },
        tenant: {
          id: 2,
          username: 'tenant1',
          full_name: '张三',
          phone_number: '13800138000'
        },
        description: '厨房水龙头漏水，每分钟滴水约10滴，已经持续一周了。希望能尽快修理，以免浪费水资源。',
        urgency_level: '一般',
        images: [
          { id: 1, url: 'https://via.placeholder.com/800x600' },
          { id: 2, url: 'https://via.placeholder.com/800x600' }
        ],
        status: '处理中',
        landlord_response: '已安排维修人员，预计明天上午10点到达，请保持电话畅通。',
        completed_at: null,
        submitted_at: '2023-05-15T10:30:00',
        updated_at: '2023-05-15T14:45:00'
      }
      
      loading.value = false
    }, 500)
    
    // 实际API调用应该类似于:
    // const response = await api.get(`/repairs/${repairId.value}`)
    // repair.value = response.data
  } catch (error) {
    console.error('Failed to fetch repair detail:', error)
    ElMessage.error('获取维修请求详情失败')
    loading.value = false
  }
}

// 查看图片
const viewImage = (url: string) => {
  currentImage.value = url
  imageDialogVisible.value = true
}

// 返回列表
const goBack = () => {
  router.push('/tenant/repairs')
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '暂无'
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

// 紧急程度映射
const urgencyMap = {
  '紧急': 'danger',
  '一般': 'warning',
  '低': 'info'
}

onMounted(() => {
  fetchRepairDetail()
})
</script>

<template>
  <div class="repair-detail-container">
    <div class="page-header">
      <el-button @click="goBack" icon="arrow-left">返回列表</el-button>
      <h1>维修请求详情</h1>
    </div>
    
    <div class="repair-detail" v-loading="loading">
      <template v-if="repair">
        <!-- 基本信息 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h2>基本信息</h2>
              <div class="status-tags">
                <el-tag
                  :type="
                    repair.status === '已完成' ? 'success' :
                    repair.status === '处理中' ? 'warning' : 'info'
                  "
                >
                  {{ repair.status }}
                </el-tag>
                <el-tag :type="urgencyMap[repair.urgency_level as keyof typeof urgencyMap]">
                  {{ repair.urgency_level }}
                </el-tag>
              </div>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="房屋信息">
              {{ repair.house.title }}
            </el-descriptions-item>
            <el-descriptions-item label="房屋地址">
              {{ repair.house.address }}
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">
              {{ formatDateTime(repair.submitted_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(repair.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="完成时间" :span="2">
              {{ formatDateTime(repair.completed_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 问题描述 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h2>问题描述</h2>
            </div>
          </template>
          
          <div class="description">
            {{ repair.description }}
          </div>
        </el-card>
        
        <!-- 房东回复 -->
        <el-card class="detail-card" v-if="repair.landlord_response">
          <template #header>
            <div class="card-header">
              <h2>房东回复</h2>
            </div>
          </template>
          
          <div class="landlord-response">
            {{ repair.landlord_response }}
          </div>
        </el-card>
        
        <!-- 问题图片 -->
        <el-card class="detail-card" v-if="repair.images && repair.images.length > 0">
          <template #header>
            <div class="card-header">
              <h2>问题图片</h2>
            </div>
          </template>
          
          <div class="image-gallery">
            <div 
              v-for="image in repair.images" 
              :key="image.id" 
              class="image-item"
              @click="viewImage(image.url)"
            >
              <img :src="image.url" :alt="`图片 ${image.id}`">
            </div>
          </div>
        </el-card>
      </template>
      
      <el-empty v-else-if="!loading" description="维修请求不存在" />
    </div>
    
    <!-- 图片查看对话框 -->
    <el-dialog v-model="imageDialogVisible" title="查看图片" width="80%">
      <div class="image-dialog-content">
        <img :src="currentImage" alt="查看图片">
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.repair-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 0 15px;
  font-size: 24px;
  color: #303133;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.status-tags {
  display: flex;
  gap: 10px;
}

.description, .landlord-response {
  white-space: pre-line;
  line-height: 1.6;
  color: #303133;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.image-item {
  width: 200px;
  height: 150px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s;
}

.image-item:hover {
  transform: scale(1.05);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-dialog-content {
  display: flex;
  justify-content: center;
}

.image-dialog-content img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
</style>
