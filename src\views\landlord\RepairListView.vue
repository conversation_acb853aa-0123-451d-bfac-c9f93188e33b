<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import repairService from '@/services/repair.service'
import type { Repair } from '@/services/repair.service'

const router = useRouter()

// 维修请求列表数据
const repairs = ref<Repair[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  urgency_level: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待处理', label: '待处理' },
  { value: '处理中', label: '处理中' },
  { value: '已完成', label: '已完成' }
]

// 紧急程度选项
const urgencyOptions = [
  { value: '', label: '全部' },
  { value: '紧急', label: '紧急' },
  { value: '一般', label: '一般' },
  { value: '低', label: '低' }
]

// 紧急程度映射
const urgencyMap = {
  '紧急': 'danger',
  '一般': 'warning',
  '低': 'info'
}

// 获取维修请求列表
const fetchRepairs = async () => {
  try {
    loading.value = true
    
    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await repairService.getRepairList(params)
    repairs.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch repairs:', error)
    ElMessage.error('获取维修请求列表失败')
  } finally {
    loading.value = false
  }
}

// 处理维修请求
const processRepair = (repairId: number) => {
  ElMessageBox.prompt('请输入处理回复', '处理维修请求', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入您的处理方案和预计完成时间'
  }).then(async ({ value }) => {
    try {
      await repairService.processRepair(repairId, {
        landlord_response: value || '正在处理中'
      })
      
      // 更新状态
      const index = repairs.value.findIndex(item => item.id === repairId)
      if (index !== -1) {
        repairs.value[index].status = '处理中'
        repairs.value[index].landlord_response = value || '正在处理中'
      }
      
      ElMessage.success('维修请求已处理')
    } catch (error) {
      console.error('Failed to process repair:', error)
      ElMessage.error('处理维修请求失败')
    }
  }).catch(() => {})
}

// 完成维修请求
const completeRepair = (repairId: number) => {
  ElMessageBox.prompt('请输入完成说明', '完成维修', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入维修完成的详细说明'
  }).then(async ({ value }) => {
    try {
      await repairService.completeRepair(repairId, {
        completion_notes: value || '维修已完成'
      })
      
      // 更新状态
      const index = repairs.value.findIndex(item => item.id === repairId)
      if (index !== -1) {
        repairs.value[index].status = '已完成'
        repairs.value[index].completed_at = new Date().toISOString()
      }
      
      ElMessage.success('维修已完成')
    } catch (error) {
      console.error('Failed to complete repair:', error)
      ElMessage.error('完成维修失败')
    }
  }).catch(() => {})
}

// 查看维修详情
const viewRepairDetail = (repairId: number) => {
  router.push(`/landlord/repairs/${repairId}`)
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchRepairs()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchRepairs()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '暂无'
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchRepairs()
})
</script>

<template>
  <div class="repair-list-container">
    <div class="page-header">
      <h1>维修管理</h1>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="紧急程度">
          <el-select v-model="filters.urgency_level" placeholder="选择紧急程度" clearable @change="handleFilterChange">
            <el-option
              v-for="item in urgencyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="repair-list" v-loading="loading">
      <el-empty v-if="repairs.length === 0 && !loading" description="暂无维修请求记录" />
      
      <el-table :data="repairs" style="width: 100%">
        <el-table-column label="房源信息" width="200">
          <template #default="{ row }">
            <div class="house-info">
              <div class="house-title" @click="viewHouseDetail(row.house.id)">
                {{ row.house.title }}
              </div>
              <div class="house-address">{{ row.house.address }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租客信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="tenant-name">{{ row.tenant.full_name }}</div>
              <div class="tenant-phone">{{ row.tenant.phone_number }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="问题描述" min-width="200">
          <template #default="{ row }">
            <div class="description-text">{{ row.description }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="urgency_level" label="紧急程度" width="120">
          <template #default="{ row }">
            <el-tag :type="urgencyMap[row.urgency_level as keyof typeof urgencyMap]">
              {{ row.urgency_level }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '已完成' ? 'success' :
                row.status === '处理中' ? 'warning' : 'info'
              "
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="submitted_at" label="提交时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.submitted_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="completed_at" label="完成时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.completed_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewRepairDetail(row.id)"
            >
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              v-if="row.status === '待处理'"
              @click="processRepair(row.id)"
            >
              处理
            </el-button>
            <el-button
              type="success"
              size="small"
              v-if="row.status === '处理中'"
              @click="completeRepair(row.id)"
            >
              完成
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.repair-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.repair-list {
  min-height: 400px;
}

.house-info {
  display: flex;
  flex-direction: column;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
  cursor: pointer;
  color: #303133;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.tenant-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.tenant-phone {
  font-size: 12px;
  color: #909399;
}

.description-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
