<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import appointmentService from '@/services/appointment.service'
import type { Appointment } from '@/services/appointment.service'

const router = useRouter()
const appointments = ref<Appointment[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待确认', label: '待确认' },
  { value: '已确认', label: '已确认' },
  { value: '已取消', label: '已取消' },
  { value: '已完成', label: '已完成' }
]

// 获取预约列表
const fetchAppointments = async () => {
  try {
    loading.value = true

    const response = await appointmentService.getAppointmentList({
      status: filters.status,
      page: filters.page,
      per_page: filters.per_page
    })

    appointments.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch appointments:', error)
    ElMessage.error('获取预约列表失败')
  } finally {
    loading.value = false
  }
}

// 取消预约
const cancelAppointment = (appointmentId: number) => {
  ElMessageBox.confirm('确定要取消该预约吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await appointmentService.cancelAppointment(appointmentId, {
        reason: '个人原因取消'
      })

      // 更新状态
      const index = appointments.value.findIndex(item => item.id === appointmentId)
      if (index !== -1) {
        appointments.value[index].status = '已取消'
      }

      ElMessage.success('预约已取消')
    } catch (error) {
      console.error('Failed to cancel appointment:', error)
      ElMessage.error('取消预约失败')
    }
  }).catch(() => {})
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchAppointments()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchAppointments()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

// 创建新预约
const createAppointment = () => {
  router.push('/houses')
}

onMounted(() => {
  fetchAppointments()
})
</script>

<template>
  <div class="appointment-list-container">
    <div class="page-header">
      <h1>看房预约</h1>
      <el-button type="primary" @click="createAppointment">预约看房</el-button>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <div class="appointment-list" v-loading="loading">
      <el-empty v-if="appointments.length === 0 && !loading" description="暂无预约记录" />

      <el-card v-for="appointment in appointments" :key="appointment.id" class="appointment-item">
        <div class="appointment-content">
          <div class="house-image" @click="viewHouseDetail(appointment.house.id)">
            <img :src="appointment.house.main_image_url" :alt="appointment.house.title">
          </div>

          <div class="appointment-info">
            <h3 class="house-title" @click="viewHouseDetail(appointment.house.id)">
              {{ appointment.house.title }}
            </h3>

            <p class="house-address">
              {{ appointment.house.address }}
            </p>

            <div class="appointment-details">
              <div class="detail-item">
                <span class="label">预约时间:</span>
                <span class="value">{{ formatDateTime(appointment.appointment_time) }}</span>
              </div>

              <div class="detail-item">
                <span class="label">状态:</span>
                <el-tag
                  :type="
                    appointment.status === '已确认' ? 'success' :
                    appointment.status === '待确认' ? 'warning' :
                    appointment.status === '已取消' ? 'info' : 'primary'
                  "
                >
                  {{ appointment.status }}
                </el-tag>
              </div>

              <div class="detail-item" v-if="appointment.message">
                <span class="label">留言:</span>
                <span class="value">{{ appointment.message }}</span>
              </div>

              <div class="detail-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDateTime(appointment.created_at) }}</span>
              </div>
            </div>
          </div>

          <div class="appointment-actions">
            <el-button type="primary" @click="viewHouseDetail(appointment.house.id)">查看房源</el-button>
            <el-button
              type="danger"
              @click="cancelAppointment(appointment.id)"
              :disabled="appointment.status === '已取消' || appointment.status === '已完成'"
            >
              取消预约
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.appointment-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.appointment-list {
  min-height: 400px;
}

.appointment-item {
  margin-bottom: 20px;
}

.appointment-content {
  display: flex;
  align-items: flex-start;
}

.house-image {
  width: 150px;
  height: 100px;
  overflow: hidden;
  border-radius: 4px;
  margin-right: 20px;
  cursor: pointer;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.house-image:hover img {
  transform: scale(1.05);
}

.appointment-info {
  flex: 1;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
  cursor: pointer;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  margin: 0 0 10px 0;
  color: #909399;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.label {
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #303133;
}

.appointment-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
