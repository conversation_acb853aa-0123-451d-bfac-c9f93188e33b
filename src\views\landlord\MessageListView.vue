<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import messageService from '@/services/message.service'
import type { Message } from '@/services/message.service'

// 消息列表数据
const messages = ref<Message[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '未读', label: '未读' },
  { value: '已读', label: '已读' }
]

// 发送消息对话框
const sendDialogVisible = ref(false)
const sendForm = reactive({
  recipient_id: 0,
  subject: '',
  content: ''
})

// 租客选项
const tenantOptions = ref<{ value: number; label: string }[]>([])

// 获取消息列表
const fetchMessages = async () => {
  try {
    loading.value = true
    
    const response = await messageService.getMessageList({
      status: filters.status,
      page: filters.page,
      per_page: filters.per_page
    })
    
    messages.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch messages:', error)
    ElMessage.error('获取消息列表失败')
  } finally {
    loading.value = false
  }
}

// 标记为已读
const markAsRead = async (messageId: number) => {
  try {
    await messageService.markAsRead(messageId)
    
    // 更新状态
    const index = messages.value.findIndex(item => item.id === messageId)
    if (index !== -1) {
      messages.value[index].status = '已读'
      messages.value[index].read_at = new Date().toISOString()
    }
    
    ElMessage.success('已标记为已读')
  } catch (error) {
    console.error('Failed to mark as read:', error)
    ElMessage.error('标记失败')
  }
}

// 回复消息
const replyMessage = (message: Message) => {
  sendForm.recipient_id = message.sender.id
  sendForm.subject = `回复: ${message.subject}`
  sendForm.content = ''
  sendDialogVisible.value = true
}

// 发送消息
const sendMessage = async () => {
  try {
    await messageService.sendMessage(sendForm)
    
    ElMessage.success('消息发送成功')
    sendDialogVisible.value = false
    
    // 重置表单
    Object.assign(sendForm, {
      recipient_id: 0,
      subject: '',
      content: ''
    })
    
    // 刷新列表
    fetchMessages()
  } catch (error) {
    console.error('Failed to send message:', error)
    ElMessage.error('发送消息失败')
  }
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchMessages()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchMessages()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchMessages()
})
</script>

<template>
  <div class="message-list-container">
    <div class="page-header">
      <h1>我的消息</h1>
      <el-button type="primary" @click="sendDialogVisible = true">发送消息</el-button>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="message-list" v-loading="loading">
      <el-empty v-if="messages.length === 0 && !loading" description="暂无消息记录" />
      
      <el-table :data="messages" style="width: 100%">
        <el-table-column label="发送人" width="150">
          <template #default="{ row }">
            <div>
              <div class="sender-name">{{ row.sender.full_name }}</div>
              <div class="sender-role">{{ row.sender.role === 'tenant' ? '租客' : '房东' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="subject" label="主题" min-width="200">
          <template #default="{ row }">
            <div class="subject-text" :class="{ unread: row.status === '未读' }">
              {{ row.subject }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="content" label="内容" min-width="250">
          <template #default="{ row }">
            <div class="content-text">{{ row.content }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '已读' ? 'success' : 'warning'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sent_at" label="发送时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.sent_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              v-if="row.status === '未读'"
              @click="markAsRead(row.id)"
            >
              标记已读
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="replyMessage(row)"
            >
              回复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
    
    <!-- 发送消息对话框 -->
    <el-dialog
      v-model="sendDialogVisible"
      title="发送消息"
      width="600px"
    >
      <el-form :model="sendForm" label-width="80px">
        <el-form-item label="收件人" required>
          <el-select v-model="sendForm.recipient_id" placeholder="选择收件人" style="width: 100%">
            <el-option
              v-for="tenant in tenantOptions"
              :key="tenant.value"
              :label="tenant.label"
              :value="tenant.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="主题" required>
          <el-input v-model="sendForm.subject" placeholder="请输入消息主题" />
        </el-form-item>
        
        <el-form-item label="内容" required>
          <el-input
            v-model="sendForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入消息内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sendDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendMessage">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.message-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.message-list {
  min-height: 400px;
}

.sender-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.sender-role {
  font-size: 12px;
  color: #909399;
}

.subject-text {
  font-weight: normal;
}

.subject-text.unread {
  font-weight: bold;
  color: #409eff;
}

.content-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 230px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
