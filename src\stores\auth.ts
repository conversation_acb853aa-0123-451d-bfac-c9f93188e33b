import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService, { LoginRequest, RegisterRequest } from '@/services/auth.service'
import { ElMessage } from 'element-plus'

export interface User {
  id: number
  username: string
  role: string
  email?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isLandlord = computed(() => user.value?.role === 'landlord')
  const isTenant = computed(() => user.value?.role === 'tenant')
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化状态
  const initialize = () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')

    if (storedToken) {
      token.value = storedToken
    }

    if (storedUser) {
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('Failed to parse user from localStorage', error)
        localStorage.removeItem('user')
      }
    }
  }

  // 登录
  const login = async (loginData: LoginRequest) => {
    loading.value = true

    try {
      const response = await authService.login(loginData)

      token.value = response.token
      user.value = {
        id: response.id,
        username: response.username,
        role: response.role
      }

      ElMessage.success('登录成功')

      // 根据用户角色跳转到不同页面
      if (response.role === 'landlord') {
        window.location.href = '/landlord/dashboard'
      } else if (response.role === 'tenant') {
        window.location.href = '/tenant/dashboard'
      } else if (response.role === 'admin') {
        window.location.href = '/admin/dashboard'
      } else {
        window.location.href = '/'
      }

      return response
    } catch (error) {
      console.error('Login error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerData: RegisterRequest) => {
    loading.value = true

    try {
      const response = await authService.register(registerData)

      token.value = response.token
      user.value = {
        id: response.id,
        username: response.username,
        email: response.email,
        role: response.role
      }

      ElMessage.success('注册成功')

      // 根据用户角色跳转到不同页面
      if (response.role === 'landlord') {
        window.location.href = '/landlord/dashboard'
      } else if (response.role === 'tenant') {
        window.location.href = '/tenant/dashboard'
      } else {
        window.location.href = '/'
      }

      return response
    } catch (error) {
      console.error('Register error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    loading.value = true

    try {
      await authService.logout()

      // 清除状态
      token.value = null
      user.value = null

      // 跳转到登录页
      window.location.href = '/login'

      ElMessage.success('已成功退出登录')
    } catch (error) {
      console.error('Logout error:', error)

      // 即使API调用失败，也清除本地状态
      token.value = null
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')

      window.location.href = '/login'
    } finally {
      loading.value = false
    }
  }

  // 刷新令牌
  const refreshToken = async () => {
    try {
      const response = await authService.refreshToken()
      token.value = response.token
      return response
    } catch (error) {
      console.error('Refresh token error:', error)
      throw error
    }
  }

  return {
    user,
    token,
    loading,
    isLoggedIn,
    isLandlord,
    isTenant,
    isAdmin,
    initialize,
    login,
    register,
    logout,
    refreshToken
  }
})
