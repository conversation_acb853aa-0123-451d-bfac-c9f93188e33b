import api from './api'

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  role: 'landlord' | 'tenant' | 'admin'
  full_name: string
  phone_number: string
}

export interface AuthResponse {
  id: number
  username: string
  email?: string
  role: string
  token: string
}

export interface RefreshTokenResponse {
  token: string
}

export interface MessageResponse {
  message: string
}

export interface ChangePasswordRequest {
  current_password: string
  new_password: string
}

export interface MFASetupResponse {
  qr_code: string
  secret: string
  backup_codes: string[]
}

export interface MFAVerifyRequest {
  code: string
}

class AuthService {
  /**
   * 用户登录
   * @param data 登录信息
   */
  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/login', data)

    // 保存token和用户信息到localStorage
    if (response.data.token) {
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('user', JSON.stringify({
        id: response.data.id,
        username: response.data.username,
        role: response.data.role
      }))
    }

    return response.data
  }

  /**
   * 用户注册
   * @param data 注册信息
   */
  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/register', data)

    // 保存token和用户信息到localStorage
    if (response.data.token) {
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('user', JSON.stringify({
        id: response.data.id,
        username: response.data.username,
        email: response.data.email,
        role: response.data.role
      }))
    }

    return response.data
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    const response = await api.post<RefreshTokenResponse>('/auth/refresh')

    // 更新localStorage中的token
    if (response.data.token) {
      localStorage.setItem('token', response.data.token)
    }

    return response.data
  }

  /**
   * 退出登录
   */
  async logout(): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/auth/logout')

    // 清除localStorage中的token和用户信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')

    return response.data
  }

  /**
   * 获取当前登录用户
   */
  getCurrentUser() {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      return JSON.parse(userStr)
    }
    return null
  }

  /**
   * 检查用户是否已登录
   */
  isLoggedIn(): boolean {
    return !!localStorage.getItem('token')
  }

  /**
   * 修改密码
   * @param data 密码修改信息
   */
  async changePassword(data: ChangePasswordRequest): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/auth/change_password', data)
    return response.data
  }

  /**
   * 设置多因素认证
   */
  async setupMFA(): Promise<MFASetupResponse> {
    const response = await api.post<MFASetupResponse>('/auth/setup_mfa')
    return response.data
  }

  /**
   * 验证MFA设置
   * @param data 验证码
   */
  async verifyMFA(data: MFAVerifyRequest): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/auth/verify_mfa', data)
    return response.data
  }

  /**
   * 禁用MFA
   */
  async disableMFA(): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>('/auth/disable_mfa')
    return response.data
  }
}

export default new AuthService()
