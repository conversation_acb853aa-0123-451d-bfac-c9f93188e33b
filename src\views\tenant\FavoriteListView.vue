<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import favoriteService from '@/services/favorite.service'
import type { FavoriteHouse } from '@/services/favorite.service'

const router = useRouter()
const favorites = ref<FavoriteHouse[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 获取收藏列表
const fetchFavorites = async () => {
  try {
    loading.value = true

    const response = await favoriteService.getFavoriteList({
      page: currentPage.value,
      per_page: pageSize.value
    })

    favorites.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch favorites:', error)
    ElMessage.error('获取收藏列表失败')
  } finally {
    loading.value = false
  }
}

// 取消收藏
const cancelFavorite = (favoriteId: number) => {
  ElMessageBox.confirm('确定要取消收藏该房源吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await favoriteService.removeFavorite(favoriteId)

      // 从列表中移除
      favorites.value = favorites.value.filter(item => item.id !== favoriteId)
      total.value--

      ElMessage.success('已取消收藏')
    } catch (error) {
      console.error('Failed to cancel favorite:', error)
      ElMessage.error('取消收藏失败')
    }
  }).catch(() => {})
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchFavorites()
}

onMounted(() => {
  fetchFavorites()
})
</script>

<template>
  <div class="favorite-list-container">
    <div class="page-header">
      <h1>我的收藏</h1>
    </div>

    <div class="favorite-list" v-loading="loading">
      <el-empty v-if="favorites.length === 0 && !loading" description="暂无收藏房源" />

      <el-card v-for="favorite in favorites" :key="favorite.id" class="favorite-item">
        <div class="favorite-content">
          <div class="house-image" @click="viewHouseDetail(favorite.house.id)">
            <img :src="favorite.house.main_image_url" :alt="favorite.house.title">
          </div>

          <div class="house-info">
            <h3 class="house-title" @click="viewHouseDetail(favorite.house.id)">
              {{ favorite.house.title }}
            </h3>

            <p class="house-address">
              {{ favorite.house.address }}
            </p>

            <div class="house-details">
              <span class="house-price">¥{{ favorite.house.price_monthly }}/月</span>
              <span class="house-type">{{ favorite.house.bedrooms }}室</span>
              <span class="house-location">{{ favorite.house.district }}</span>
            </div>

            <div class="favorite-date">
              收藏于: {{ favorite.created_at }}
            </div>
          </div>

          <div class="favorite-actions">
            <el-button type="primary" @click="viewHouseDetail(favorite.house.id)">查看详情</el-button>
            <el-button type="danger" @click="cancelFavorite(favorite.id)">取消收藏</el-button>
          </div>
        </div>
      </el-card>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.favorite-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.favorite-list {
  min-height: 400px;
}

.favorite-item {
  margin-bottom: 20px;
}

.favorite-content {
  display: flex;
  align-items: center;
}

.house-image {
  width: 150px;
  height: 100px;
  overflow: hidden;
  border-radius: 4px;
  margin-right: 20px;
  cursor: pointer;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.house-image:hover img {
  transform: scale(1.05);
}

.house-info {
  flex: 1;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #303133;
  cursor: pointer;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  margin: 0 0 10px 0;
  color: #909399;
}

.house-details {
  margin-bottom: 10px;
}

.house-price {
  color: #f56c6c;
  font-weight: bold;
  margin-right: 15px;
}

.house-type, .house-location {
  color: #606266;
  margin-right: 15px;
}

.favorite-date {
  font-size: 12px;
  color: #909399;
}

.favorite-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
