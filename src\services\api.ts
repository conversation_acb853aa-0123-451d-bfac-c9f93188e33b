import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import router from '@/router'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 错误码映射
const errorCodeMap: Record<string, string> = {
  '400': '请求参数错误',
  '401': '未授权，请重新登录',
  '403': '拒绝访问',
  '404': '请求错误，未找到该资源',
  '409': '资源冲突',
  '422': '请求参数验证失败',
  '429': '请求过于频繁',
  '500': '服务器内部错误',
  '1001': '用户名或密码错误',
  '1002': '账户已被锁定',
  '1003': '验证码错误或已过期',
  '1004': '账户余额不足',
  '1005': '房源已被出租',
  '1006': '租约已存在',
  '1007': '不允许的操作',
  '1008': '文件上传失败',
  '1009': '文件类型不支持',
  '1010': '文件大小超出限制'
}

// 设置请求拦截器
export const setupAxiosInterceptors = () => {
  api.interceptors.request.use(
    (config: AxiosRequestConfig) => {
      // 从localStorage获取token
      const token = localStorage.getItem('token')
      
      // 如果有token则添加到请求头
      if (token && config.headers) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
      
      return config
    },
    (error: AxiosError) => {
      console.error('Request error:', error)
      return Promise.reject(error)
    }
  )

  // 设置响应拦截器
  api.interceptors.response.use(
    (response: AxiosResponse) => {
      return response
    },
    (error: AxiosError) => {
      if (error.response) {
        const { status, data } = error.response
        
        // 处理401未授权错误
        if (status === 401) {
          // 清除本地存储的token
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          
          // 如果不是登录页面，则跳转到登录页
          if (router.currentRoute.value.path !== '/login') {
            router.push('/login')
            ElMessage.error('登录已过期，请重新登录')
          }
        } else {
          // 处理其他错误
          const errorCode = status.toString()
          const errorMsg = errorCodeMap[errorCode] || (data as any)?.message || '未知错误'
          ElMessage.error(errorMsg)
        }
      } else {
        // 处理网络错误
        ElMessage.error('网络错误，请检查您的网络连接')
      }
      
      return Promise.reject(error)
    }
  )
}

export default api
