import api from './api'

export interface DashboardStats {
  total_houses: number
  total_users: number
  total_leases: number
  total_repairs: number
  active_leases: number
  pending_repairs: number
  monthly_revenue: number
  occupancy_rate: number
}

export interface RevenueReport {
  month: string
  revenue: number
  lease_count: number
}

export interface HouseReport {
  house_id: number
  house_title: string
  total_revenue: number
  lease_count: number
  repair_count: number
  occupancy_days: number
}

export interface UserReport {
  user_id: number
  username: string
  full_name: string
  role: string
  lease_count: number
  total_spent: number
  registration_date: string
}

class ReportService {
  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await api.get<DashboardStats>('/reports/dashboard')
    return response.data
  }
  
  /**
   * 获取收入报表
   * @param params 查询参数
   */
  async getRevenueReport(params?: {
    start_date?: string
    end_date?: string
    period?: 'month' | 'quarter' | 'year'
  }): Promise<RevenueReport[]> {
    const response = await api.get<RevenueReport[]>('/reports/revenue', { params })
    return response.data
  }
  
  /**
   * 获取房源报表
   * @param params 查询参数
   */
  async getHouseReport(params?: {
    start_date?: string
    end_date?: string
  }): Promise<HouseReport[]> {
    const response = await api.get<HouseReport[]>('/reports/houses', { params })
    return response.data
  }
  
  /**
   * 获取用户报表
   * @param params 查询参数
   */
  async getUserReport(params?: {
    role?: string
    start_date?: string
    end_date?: string
  }): Promise<UserReport[]> {
    const response = await api.get<UserReport[]>('/reports/users', { params })
    return response.data
  }
  
  /**
   * 导出报表
   * @param reportType 报表类型
   * @param params 查询参数
   */
  async exportReport(reportType: 'revenue' | 'houses' | 'users', params?: any): Promise<Blob> {
    const response = await api.get(`/reports/export/${reportType}`, {
      params,
      responseType: 'blob'
    })
    return response.data
  }
}

export default new ReportService()
