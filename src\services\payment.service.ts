import api from './api'

export interface Payment {
  id: number
  contract: {
    id: number
    house_title: string
    tenant_name: string
    landlord_name: string
  }
  amount: number
  payment_type: string
  payment_date: string
  due_date: string
  status: string
  payment_method?: string
  transaction_id?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface CreatePaymentRequest {
  contract_id: number
  amount: number
  payment_type: string
  due_date: string
  notes?: string
}

export interface ProcessPaymentRequest {
  payment_method: string
  transaction_id?: string
  notes?: string
}

export interface MessageResponse {
  message: string
}

class PaymentService {
  /**
   * 创建支付记录
   * @param contractId 合同ID
   * @param data 支付信息
   */
  async createPayment(contractId: number, data: CreatePaymentRequest): Promise<{ id: number; message: string }> {
    const response = await api.post<{ id: number; message: string }>(`/leases/contracts/${contractId}/payments`, data)
    return response.data
  }

  /**
   * 获取合同支付记录
   * @param contractId 合同ID
   * @param params 查询参数
   */
  async getContractPayments(contractId: number, params?: {
    status?: string
    payment_type?: string
  }): Promise<Payment[]> {
    const response = await api.get<Payment[]>(`/leases/contracts/${contractId}/payments`, { params })
    return response.data
  }

  /**
   * 获取支付详情
   * @param paymentId 支付ID
   */
  async getPaymentDetail(paymentId: number): Promise<Payment> {
    const response = await api.get<Payment>(`/leases/payments/${paymentId}`)
    return response.data
  }

  /**
   * 处理支付
   * @param paymentId 支付ID
   * @param data 支付处理信息
   */
  async processPayment(paymentId: number, data: ProcessPaymentRequest): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>(`/leases/payments/${paymentId}/process`, data)
    return response.data
  }

  /**
   * 获取我的支付记录
   * @param params 查询参数
   */
  async getMyPayments(params?: {
    status?: string
    payment_type?: string
    start_date?: string
    end_date?: string
  }): Promise<Payment[]> {
    const response = await api.get<Payment[]>('/leases/payments/my', { params })
    return response.data
  }
}

export default new PaymentService()
