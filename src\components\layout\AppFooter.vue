<template>
  <el-footer class="app-footer">
    <div class="footer-content">
      <div class="footer-section">
        <h3>关于我们</h3>
        <p>智能房屋租赁系统致力于为用户提供便捷、高效的房屋租赁服务。</p>
      </div>
      <div class="footer-section">
        <h3>联系方式</h3>
        <p>电话: 400-123-4567</p>
        <p>邮箱: <EMAIL></p>
      </div>
      <div class="footer-section">
        <h3>快速链接</h3>
        <ul>
          <li><router-link to="/">首页</router-link></li>
          <li><router-link to="/houses">房源列表</router-link></li>
          <li><router-link to="/login">登录</router-link></li>
          <li><router-link to="/register">注册</router-link></li>
        </ul>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; {{ currentYear }} 智能房屋租赁系统. 保留所有权利.</p>
    </div>
  </el-footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.app-footer {
  background-color: #f5f7fa;
  padding: 30px 20px;
  color: #606266;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.footer-section {
  flex: 1;
  min-width: 200px;
  margin-bottom: 20px;
  padding: 0 15px;
}

.footer-section h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #303133;
}

.footer-section p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #606266;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section ul li a:hover {
  color: var(--el-color-primary);
}

.footer-bottom {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dcdfe6;
}
</style>
