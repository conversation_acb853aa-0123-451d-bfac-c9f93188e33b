<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import messageService from '@/services/message.service'
import type { Message } from '@/services/message.service'

// 消息列表数据
const messages = ref<Message[]>([])
const loading = ref(true)

// 筛选条件
const filters = ref({
  type: 'received',
  is_read: undefined as boolean | undefined
})

// 消息类型选项
const typeOptions = [
  { label: '收到的消息', value: 'received' },
  { label: '发送的消息', value: 'sent' }
]

// 读取状态选项
const readOptions = [
  { label: '全部', value: undefined },
  { label: '已读', value: true },
  { label: '未读', value: false }
]

// 获取消息列表
const fetchMessages = async () => {
  try {
    loading.value = true
    const params = Object.entries(filters.value).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await messageService.getMessageList(params)
    messages.value = response
  } catch (error) {
    console.error('Failed to fetch messages:', error)
    ElMessage.error('获取消息列表失败')
    messages.value = []
  } finally {
    loading.value = false
  }
}

// 标记为已读
const markAsRead = async (messageId: number) => {
  try {
    await messageService.markAsRead(messageId)
    ElMessage.success('标记为已读')
    fetchMessages()
  } catch (error) {
    console.error('Failed to mark as read:', error)
    ElMessage.error('操作失败')
  }
}

// 删除消息
const deleteMessage = async (messageId: number) => {
  try {
    await messageService.deleteMessage(messageId)
    ElMessage.success('删除成功')
    fetchMessages()
  } catch (error) {
    console.error('Failed to delete message:', error)
    ElMessage.error('删除失败')
  }
}

// 筛选变化
const handleFilterChange = () => {
  fetchMessages()
}

// 格式化消息类型
const formatMessageType = (type: string) => {
  return type === 'received' ? '收到' : '发送'
}

// 获取读取状态标签类型
const getReadStatusType = (isRead: boolean) => {
  return isRead ? 'success' : 'warning'
}

// 格式化读取状态
const formatReadStatus = (isRead: boolean) => {
  return isRead ? '已读' : '未读'
}

onMounted(() => {
  fetchMessages()
})
</script>

<template>
  <div class="message-list">
    <div class="page-header">
      <h1>消息管理</h1>
      <p>查看和管理您的消息</p>
    </div>
    
    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-row :gutter="20" align="middle">
        <el-col :span="6">
          <el-select v-model="filters.type" @change="handleFilterChange">
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        
        <el-col :span="6">
          <el-select v-model="filters.is_read" @change="handleFilterChange" clearable>
            <el-option
              v-for="option in readOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        
        <el-col :span="12">
          <el-button type="primary" @click="fetchMessages" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 消息列表 -->
    <el-card class="table-card">
      <el-table :data="messages" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="发送者" width="120">
          <template #default="{ row }">
            {{ row.sender.full_name || row.sender.username }}
          </template>
        </el-table-column>
        
        <el-table-column label="接收者" width="120">
          <template #default="{ row }">
            {{ row.receiver.full_name || row.receiver.username }}
          </template>
        </el-table-column>
        
        <el-table-column prop="subject" label="主题" width="200" />
        
        <el-table-column prop="content" label="内容" min-width="300">
          <template #default="{ row }">
            <div class="message-content">
              {{ row.content.length > 100 ? row.content.substring(0, 100) + '...' : row.content }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="is_read" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReadStatusType(row.is_read)">
              {{ formatReadStatus(row.is_read) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="!row.is_read && filters.type === 'received'" 
              type="primary" 
              size="small" 
              link
              @click="markAsRead(row.id)"
            >
              <el-icon><Check /></el-icon>
              标记已读
            </el-button>
            <el-button type="danger" size="small" link @click="deleteMessage(row.id)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-empty v-if="!loading && messages.length === 0" description="暂无消息" />
    </el-card>
  </div>
</template>

<style scoped>
.message-list {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  background: white;
}

.el-select {
  width: 100%;
}

.message-content {
  line-height: 1.4;
  word-break: break-word;
}
</style>
